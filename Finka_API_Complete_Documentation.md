# 翻咔（Finka/Aloha）API 完整文档 - 包含所有已获取信息

> 更新时间：2025-01-08 (关注列表 API 更新)
> 
> 本文档基于 Frida Hook 和网络抓包分析得出，包含所有已获取的认证信息、设备信息、API 接口等。
> 
> **最新更新**：成功抓取并确认了关注列表 API，发现数据在 `data.list` 中，且不包含封禁/注销用户。

## 目录
1. [基础信息](#1-基础信息)
2. [完整认证信息](#2-完整认证信息)
3. [完整设备信息](#3-完整设备信息)
4. [签名算法详解](#4-签名算法详解)
5. [所有已确认的 API 接口](#5-所有已确认的-api-接口)
6. [完整请求示例](#6-完整请求示例)
7. [响应格式示例](#7-响应格式示例)
8. [已知的用户 ID 示例](#8-已知的用户-id-示例)
9. [完整 Python 实现](#9-完整-python-实现)
10. [注意事项和待确认信息](#10-注意事项和待确认信息)

## 1. 基础信息

- **API Base URL**: `https://api.finka.cn`
- **App Package**: 
  - iOS: `com.wealoha.aloha`
  - Android: `Finka0a`
- **当前版本**: 8.12.0 / 8.12.1
- **认证方式**: Token-based (非 Cookie)
- **请求签名**: MD5 with salt
- **响应编码**: gzip 压缩
- **响应格式**: JSON

## 2. 完整认证信息

### 2.1 实际获取的 Token（示例）
```
X-App-Auth: aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY
```

格式解析：
- `aq8f3kx3dDvD1wngpZHQ9A`: 主认证令牌
- `x1KzYzLVeB0`: 当前用户 ID
- `uSNYoRY`: 辅助令牌

### 2.2 其他认证相关
```
X-App-User: x1KzYzLVeB0
X-Aloha-Sassid: qTYt1d3AaxSaXfwINGEbQt2qfZ/...（会话 ID，部分接口需要）
```

## 3. 完整设备信息

### 3.1 必需的设备 Headers（实际抓取值）
```http
X-App-GUID: 484837FB-D8BD-4312-A893-8F04B4EE994A
X-App-Device: Finka0a%2F8.12.0+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+*********%2FOfficial%3B%29
X-App-Package: Finka0a
X-App-Version: 8.12.0
X-App-Android-Id: oqkH9k+Tsl0BZ3PnM+/b+3PFLZJYmm6fEwMXkJkNp9c=
X-App-MacAddress: c8Utkliabp8TAxeQmQ2n1w==
X-App-BlueDeviceInfo: 3wsyM8XQzHOyoIuS1QSUqtKeZjIrHR3gvZpaOScf1mkFc1ylQyFMsqXpStxD/6f9D23pppqJHNjnkn8iT6XGfZ5g
X-App-DigitalAlliance-Id: 9pQ3QzSf7752A5fVFiwyU0GDu2BgVZXfOHr02/X7gSB1cKweXj2mr0HxE9PIYmwC
X-Android-Fingerprint: google/crosshatch/crosshatch:12/SP1A.210812.015/7679548:user/release-keys
X-Screen-Width: 1440
X-Screen-Height: 2733
X-Screen-TopSafeAreaHeight: 49
User-Agent: Finka0a/8.12.0 (Android 31/12; Google; Crosshatch; Google; Pixel 3 XL; Google Fi; *********/Official;)
Android-Store: Official
Accept-Language: en-US
```

### 3.2 通常为空的 Headers
```http
X-App-Assid: 
X-App-Imei: 
X-App-Wifi: （有时为空，有时为加密值如 sE3QNyAIlE4SZsO2Lc0PaA==）
X-App-DeviceName: （有时为空，有时为加密值）
X-App-Oaid: 
```

## 4. 签名算法详解

### 4.1 签名位置
```
Header: Digest: MD5=<32位小写十六进制>
```

### 4.2 已破解的签名算法

**关键发现**：Salt 值为 `@B<:OK6`

```python
import hashlib

def generate_digest(path, method, headers, query_params):
    """
    生成请求签名
    
    Args:
        path: API 路径，如 "/feed/user/v3/multi"
        method: HTTP 方法，如 "GET" 或 "POST"
        headers: 所有 x-app- 开头的 headers 字典
        query_params: URL 查询参数字符串（不含 ?）
    
    Returns:
        32位 MD5 签名
    """
    # 1. 构建 headers 字符串（按字母顺序）
    header_str = ""
    for key in sorted(headers.keys()):
        if key.startswith("x-app-"):
            header_str += f"{key}={headers[key]}&"
    header_str = header_str.rstrip("&")
    
    # 2. 固定 salt
    salt = "@B<:OK6"
    
    # 3. 拼接签名字符串：路径 + 方法 + headers + query + salt
    sign_str = f"{path}{method}{header_str}{query_params}{salt}"
    
    # 4. 计算 MD5
    return hashlib.md5(sign_str.encode()).hexdigest()
```

### 4.3 实际抓取的签名示例

输入：
```
路径: /feed/user/v3/multi
方法: POST
Headers: [所有 x-app- headers 按字母排序拼接]
Query: _t=1754294554026&count=21&uid=3PWRdKQiv7M8H4O5GOFG
Salt: @B<:OK6
```

拼接后的完整字符串：
```
/feed/user/v3/multiPOSTx-app-android-id=oqkH9k%2BTsl0BZ3PnM%2B%2Fb%2B3PFLZJYmm6fEwMXkJkNp9c%3D&x-app-assid=&x-app-auth=aq8f3kx3dDvD1wngpZHQ9A%7Cx1KzYzLVeB0%7CuSNYoRY&x-app-bluedeviceinfo=3wsyM8XQzHOyoIuS1QSUqtKeZjIrHR3gvZpaOScf1mkFc1ylQyFMsqXpStxD%2F6f9D23pppqJHNjnkn8iT6XGfZ5g&x-app-device=Finka0a%252F8.12.0%2B%2528Android%2B31%252F12%253B%2BGoogle%253B%2BCrosshatch%253B%2BGoogle%253B%2BPixel%2B3%2BXL%253B%2BGoogle%2BFi%253B%2B*********%252FOfficial%253B%2529&x-app-devicename=&x-app-digitalalliance-id=9pQ3QzSf7752A5fVFiwyU0GDu2BgVZXfOHr02%2FX7gSB1cKweXj2mr0HxE9PIYmwC&x-app-guid=484837FB-D8BD-4312-A893-8F04B4EE994A&x-app-imei=&x-app-macaddress=c8Utkliabp8TAxeQmQ2n1w%3D%3D&x-app-oaid=&x-app-package=Finka0a&x-app-user=x1KzYzLVeB0&x-app-version=8.12.0&x-app-wifi=_t=1754294554026&count=21&uid=3PWRdKQiv7M8H4O5GOFG@B<:OK6
```

生成的 MD5：`fc3876bc4d01442cdcce721277eaef93`

## 5. 所有已确认的 API 接口

### 5.1 作者作品列表
```
POST /feed/user/v3/multi
```

实际抓取的参数：
- `_t`: 1754294554026（毫秒时间戳）
- `count`: 21（每页数量，可选 20-30）
- `uid`: 作者 UID（如 yW_scz3AZ8o）
- `cursor`: 上一页返回的 nextCursorId（分页用，首页不传此参数）

⚠️ **重要说明**：
- 第一页请求：不需要 cursor 参数
- 后续页请求：使用 `cursor` 参数（不是 cursorId），值为上一页响应中的 `nextCursorId`
- ❌ 错误：`cursorId`
- ✅ 正确：`cursor`

响应包含：
- `itemList`: 作品数组
- `hasMore`: 是否有更多（0/1）
- `nextCursorId`: 用于下一页请求的游标（在请求时使用 cursor 参数传递）

### 5.2 用户资料查看
```
GET /user/profile/view/v3
```

实际抓取的参数：
- `_t`: *************
- `userId`: YwGHF5G1dWI（目标用户 ID）
- `forUpdate`: false
- `source`: followingUserToProfile
- `isUsingMap`: false

### 5.3 批量获取用户信息
```
POST /user/account/multi/get
```

Query 参数：
- `_t`: *************

Body (application/x-www-form-urlencoded)：
```
accountList[]=x1KzYzLVeB0&isProfile=true&isFilterNotNormalAccount=false
```

### 5.4 快捷消息指南
```
GET /vas/quickMessage/guide
```

实际参数：
- `_t`: *************
- `sceneType`: 1
- `userId`: YwGHF5G1dWI

### 5.5 关注列表（已确认）
```
POST /user/match/liked/v3/active/v3
POST /user/match/liked/v3/newest/v3
```

**重要发现**：
- 通过 Frida Hook 捕获到 `RelationUserListActivity` 启动时调用此 API
- `RelationUserListIndex = 2` 表示关注列表（0=粉丝，1=粉丝，2=关注，3=朋友）

实际抓取的参数：
- Query: `_t`: 时间戳（毫秒）
- Body (application/x-www-form-urlencoded):
  - `count`: 30（每页数量）
  - `lastId`: 分页参数（可选，用于关注列表分页）

响应格式：
```json
{
    "success": true,
    "status": 200,
    "data": {
        "list": [
            {
                "id": "用户ID",
                "name": "昵称",
                "avatar": "头像URL",
                "age": 27,
                "location": ">100km",
                "lastActiveTime": "最后活跃时间",
                // ... 其他用户信息
            }
        ],
        "hasMore": 0,  // 是否有更多页
        "lastId": ""   // 下一页参数
    }
}
```

**注意事项**：
1. 数据在 `data.list` 中，不是 `itemList`
2. 实测只返回一页数据（27个用户），`hasMore` 为 0
3. 可能不包含已封禁/注销的用户（实际关注31人，返回27人）

### 5.6 推测但未确认的接口

作品详情：
- `/feed/detail?id=<postId>`
- `/moment/detail?mid=<momentId>`

## 6. 完整请求示例

### 6.1 获取用户资料的实际请求
```http
GET https://api.finka.cn/user/profile/view/v3?_t=*************&forUpdate=false&isUsingMap=false&source=followingUserToProfile&userId=YwGHF5G1dWI

Headers:
Digest: 2ada0438c0c5f7caf75237168dd383de
X-App-Auth: aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY
Android-Store: Official
X-App-Device: Finka0a%2F8.12.0+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+*********%2FOfficial%3B%29
X-App-GUID: 484837FB-D8BD-4312-A893-8F04B4EE994A
X-App-User: x1KzYzLVeB0
Accept-Language: en-US
X-App-Version: 8.12.0
[... 其他 headers ...]
```

### 6.2 获取作品列表的实际请求
```http
POST https://api.finka.cn/feed/user/v3/multi?_t=1754294554026&count=21&uid=3PWRdKQiv7M8H4O5GOFG

Headers:
Digest: fc3876bc4d01442cdcce721277eaef93
[... 同上 ...]
```

## 7. 响应格式示例

### 7.1 作品列表响应
```json
{
    "success": true,
    "code": 200,
    "data": {
        "list": [
            {
                "postId": "xxx",
                "type": "ImagePost",
                "desc": "作品内容...",
                "imageInfoList": [...],
                "createTime": 1754294554026,
                "likeCount": 100,
                "commentCount": 20,
                // ... 其他字段
            }
        ],
        "nextCursorId": "xxx",  // 用于下一页请求，通过 cursor 参数传递
        "hasMore": true
    }
}
```

### 7.2 用户资料响应
```json
{
    "success": true,
    "code": 200,
    "data": {
        "id": "userId",
        "username": "username",
        "name": "显示名",
        "followingCount": 123,
        "followerCount": 456,
        "friendCount": 78,
        // ... 其他字段
    }
}
```

## 8. 已知的用户 ID 示例

从抓包中获取的实际用户 ID：
- 当前登录用户: `x1KzYzLVeB0`
- 示例作者 UID:
  - `yW_scz3AZ8o`（原始文档中的目标作者）
  - `3PWRdKQiv7M8H4O5GOFG`
  - `YwGHF5G1dWI`
  - `cDBA-mEYce0`
  - `aXY00Kkg28I`
  - `6d_iLJ4yOJo`

## 9. 完整 Python 实现

```python
import time
import requests
import hashlib
import gzip
import json
from urllib.parse import urlencode

class FinkaAPI:
    def __init__(self, auth_token):
        self.base_url = "https://api.finka.cn"
        self.auth_token = auth_token
        self.salt = "@B<:OK6"
        
        # 解析 auth token
        parts = auth_token.split('|')
        self.user_id = parts[1] if len(parts) > 1 else ""
        
        # 完整的设备 headers（使用实际抓取的值）
        self.device_headers = {
            "X-App-Auth": auth_token,
            "X-App-Device": "Finka0a%2F8.12.0+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+*********%2FOfficial%3B%29",
            "X-App-GUID": "484837FB-D8BD-4312-A893-8F04B4EE994A",
            "X-App-User": self.user_id,
            "X-App-Version": "8.12.0",
            "X-App-Package": "Finka0a",
            "X-App-Android-Id": "oqkH9k+Tsl0BZ3PnM+/b+3PFLZJYmm6fEwMXkJkNp9c=",
            "X-App-MacAddress": "c8Utkliabp8TAxeQmQ2n1w==",
            "X-App-BlueDeviceInfo": "3wsyM8XQzHOyoIuS1QSUqtKeZjIrHR3gvZpaOScf1mkFc1ylQyFMsqXpStxD/6f9D23pppqJHNjnkn8iT6XGfZ5g",
            "X-App-DigitalAlliance-Id": "9pQ3QzSf7752A5fVFiwyU0GDu2BgVZXfOHr02/X7gSB1cKweXj2mr0HxE9PIYmwC",
            "X-Android-Fingerprint": "google/crosshatch/crosshatch:12/SP1A.210812.015/7679548:user/release-keys",
            "X-Screen-Width": "1440",
            "X-Screen-Height": "2733",
            "X-Screen-TopSafeAreaHeight": "49",
            "X-App-Assid": "",
            "X-App-Imei": "",
            "X-App-Wifi": "",
            "X-App-DeviceName": "",
            "X-App-Oaid": "",
            "User-Agent": "Finka0a/8.12.0 (Android 31/12; Google; Crosshatch; Google; Pixel 3 XL; Google Fi; *********/Official;)",
            "Android-Store": "Official",
            "Accept-Language": "en-US",
            "Accept-Encoding": "gzip",
            "X-Version-build": "2"
        }
    
    def generate_digest(self, path, method, headers, query):
        """生成请求签名"""
        # 构建 headers 字符串（只包含 x-app- 开头的，按字母顺序）
        header_parts = []
        for key in sorted(headers.keys()):
            if key.lower().startswith("x-app-"):
                header_parts.append(f"{key.lower()}={headers[key]}")
        header_str = "&".join(header_parts)
        
        # 拼接签名字符串
        sign_str = f"{path}{method}{header_str}{query}{self.salt}"
        
        # 计算 MD5
        return hashlib.md5(sign_str.encode()).hexdigest()
    
    def request(self, method, path, params=None, data=None):
        """发送请求"""
        # 添加时间戳
        if params is None:
            params = {}
        params['_t'] = str(int(time.time() * 1000))
        
        # 构建查询字符串
        query_str = "&".join([f"{k}={v}" for k, v in params.items()])
        
        # 准备 headers
        headers = self.device_headers.copy()
        
        # 生成签名
        digest = self.generate_digest(path, method, headers, query_str)
        headers['Digest'] = f"MD5={digest}"
        headers['Host'] = 'api.finka.cn'
        headers['Connection'] = 'Keep-Alive'
        
        # 发送请求
        url = f"{self.base_url}{path}"
        
        if method == "GET":
            resp = requests.get(url, params=params, headers=headers)
        else:
            headers['Content-Type'] = 'application/x-www-form-urlencoded'
            resp = requests.post(url, params=params, data=data, headers=headers)
        
        # 解析响应
        if resp.headers.get('content-encoding') == 'gzip':
            content = gzip.decompress(resp.content)
            return json.loads(content)
        else:
            return resp.json()
    
    def get_user_profile(self, user_id):
        """获取用户资料"""
        return self.request("GET", "/user/profile/view/v3", {
            "userId": user_id,
            "forUpdate": "false",
            "source": "followingUserToProfile",
            "isUsingMap": "false"
        })
    
    def get_user_posts(self, uid, count=21, last_id=""):
        """获取用户作品列表"""
        params = {
            "count": str(count),
            "uid": uid
        }
        if last_id:
            params["lastId"] = last_id
        
        return self.request("POST", "/feed/user/v3/multi", params)
    
    def get_following_list(self, count=30, last_id=""):
        """获取关注列表
        
        注意：实测只返回一页数据，不包含封禁/注销用户
        """
        data = {"count": str(count)}
        if last_id:
            data["lastId"] = last_id
        
        result = self.request("POST", "/user/match/liked/v3/active/v3", data=urlencode(data))
        
        # 数据在 data.list 中
        if result.get("success") and result.get("data"):
            users = result["data"].get("list", [])
            has_more = result["data"].get("hasMore", 0)
            next_last_id = result["data"].get("lastId", "")
            
            return {
                "users": users,
                "hasMore": has_more,
                "lastId": next_last_id,
                "total": len(users)
            }
        
        return None
    
    def get_all_user_posts(self, uid):
        """获取用户所有作品"""
        all_posts = []
        last_id = ""
        
        while True:
            print(f"获取第 {len(all_posts)//21 + 1} 页...")
            result = self.get_user_posts(uid, last_id=last_id)
            
            if result.get("success") and result.get("itemList"):
                all_posts.extend(result["itemList"])
                print(f"当前已获取 {len(all_posts)} 个作品")
                
                if not result.get("hasMore") or result.get("hasMore") == 0:
                    break
                    
                last_id = result.get("lastId", "")
                time.sleep(1)  # 避免请求过快
            else:
                print(f"请求失败: {result}")
                break
        
        return all_posts
    
    def get_multi_users(self, user_ids):
        """批量获取用户信息"""
        data = {
            "isProfile": "true",
            "isFilterNotNormalAccount": "false"
        }
        # 添加用户 ID 列表
        for uid in user_ids:
            data[f"accountList[]"] = uid
        
        return self.request("POST", "/user/account/multi/get", data=urlencode(data, True))

# 使用示例
if __name__ == "__main__":
    # 使用实际抓取的 token
    AUTH_TOKEN = "aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY"
    
    # 初始化 API
    api = FinkaAPI(AUTH_TOKEN)
    
    # 示例1：获取用户资料
    print("=== 获取用户资料 ===")
    profile = api.get_user_profile("YwGHF5G1dWI")
    if profile.get("success"):
        data = profile["data"]
        print(f"用户名: {data.get('name')}")
        print(f"粉丝数: {data.get('followerCount')}")
        print(f"关注数: {data.get('followingCount')}")
    
    # 示例2：获取用户作品
    print("\n=== 获取用户作品 ===")
    posts = api.get_user_posts("yW_scz3AZ8o", count=5)
    if posts.get("success"):
        print(f"获取到 {len(posts['itemList'])} 个作品")
        for post in posts['itemList'][:3]:
            print(f"- {post.get('content', '')[:50]}...")
    
    # 示例3：获取关注列表
    print("\n=== 获取关注列表 ===")
    following = api.get_following_list()
    if following:
        print(f"关注了 {following['total']} 人")
        for user in following['users'][:5]:
            print(f"- {user.get('name')} (ID: {user.get('id')})")
        print(f"还有更多: {following['hasMore']}")
    
    # 示例4：获取所有作品
    # all_posts = api.get_all_user_posts("yW_scz3AZ8o")
    # print(f"共获取 {len(all_posts)} 个作品")
```

## 10. 注意事项和待确认信息

### 10.1 重要注意事项
1. **Token 时效性**: 当前 token 可能会过期，需要定期更新
2. **设备一致性**: 所有设备相关 headers 需要保持一致
3. **请求频率**: 建议请求间隔至少 1 秒，避免触发限流
4. **数据压缩**: 响应使用 gzip 压缩，需要解压
5. **签名计算**: headers 必须按字母顺序排序，且只包含 x-app- 开头的

### 10.2 已确认信息
1. **关注列表 API**: 
   - 路径: `/user/match/liked/v3/active/v3` 或 `/user/match/liked/v3/newest/v3`
   - 方法: POST
   - 数据位置: `data.list`
   - 限制: 不包含封禁/注销用户
2. **RelationUserListActivity 参数**:
   - `RelationUserListIndex = 0`: 粉丝列表
   - `RelationUserListIndex = 1`: 粉丝列表（另一种）
   - `RelationUserListIndex = 2`: 关注列表
   - `RelationUserListIndex = 3`: 朋友列表

### 10.3 待确认信息
1. **Token 过期时间**: 具体的过期规则
2. **作品详情接口**: 确切的路径和参数
3. **错误码含义**: 各种错误码的具体含义
4. **图片/视频 URL**: 高清图片和视频的获取方式
5. **粉丝列表 API**: 具体路径和参数

### 10.4 相关文件路径
```
本文档: /Users/<USER>/Finka_API_Complete_Documentation.md
Hook 脚本目录: /Users/<USER>/hook_*.js
Python 脚本: /Users/<USER>/frida_finka.py

关注列表相关文件:
- /Users/<USER>/hook_relation_activity.js - 专门监控 RelationUserListActivity
- /Users/<USER>/get_all_following.py - 获取完整关注列表脚本
- /Users/<USER>/following_list_complete_*.json - 保存的关注列表数据
- /Users/<USER>/following_list_complete_*.md - 关注列表 Markdown 报告
- /Users/<USER>/Finka_Following_API.md - 关注列表 API 专项文档
```

---

*本文档包含了所有通过 Frida Hook 和抓包获取的信息，仅供学习研究使用。*