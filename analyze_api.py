#!/usr/bin/env python3
import json

# 读取API响应
with open('/home/<USER>/downloader/api_response_user_posts.json', 'r') as f:
    data = json.load(f)

# 获取第一个作品
post = data['data']['list'][0]

# 清理评论数据
post_clean = {}
for k, v in post.items():
    if k != 'recentComments':
        post_clean[k] = v

print("API响应结构分析")
print("="*60)
print(f"API端点: GET /feed/user/v3")
print(f"响应状态: {data['status']}")
print(f"作品数量: {len(data['data']['list'])}")
print(f"下一页游标: {data['data'].get('nextCursorId', 'None')}")
print()

print("第一个作品的核心数据结构:")
print("-"*60)
print(json.dumps(post_clean, ensure_ascii=False, indent=2))
print()

# 检查所有作品的媒体字段
print("所有作品的媒体字段检查:")
print("-"*60)
for i, p in enumerate(data['data']['list'], 1):
    print(f"作品 {i}: {p.get('postId', p.get('id', ''))}")
    print(f"  类型: {p.get('type')}")
    print(f"  描述: {p.get('description', '')[:30]}...")
    print(f"  字段存在:")
    print(f"    image: {'image' in p}")
    print(f"    images: {'images' in p}")
    print(f"    video: {'video' in p}")
    print(f"    videos: {'videos' in p}")
    if 'images' in p:
        print(f"    images内容: {p['images']}")
    if 'videos' in p:
        print(f"    videos内容: {p['videos']}")
    print()