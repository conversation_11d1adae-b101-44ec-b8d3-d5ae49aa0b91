CREATE OR REPLACE FUNCTION cleanup_duplicate_anchors()
RETURNS TRIGGER AS $$
BEGIN
    -- 查询具有相同anchor_name的条目
    WITH same_name_anchors AS (
        SELECT anchor_id, anchor_name, new_url, live_url,
               ROW_NUMBER() OVER (PARTITION BY anchor_name ORDER BY anchor_id) AS rn
        FROM anchors
    ),
    -- 找到要删除的重复条目
    anchors_to_delete AS (
        SELECT anchor_id
        FROM same_name_anchors
        WHERE rn > 1
    ),
    -- 找到要保留的条目
    anchors_to_keep AS (
        SELECT anchor_id
        FROM same_name_anchors
        WHERE rn = 1
    )
    -- 删除user_subscriptions表中的相关记录,并重新插入
    DELETE FROM user_subscriptions
    WHERE anchor_id IN (SELECT anchor_id FROM anchors_to_delete);

    INSERT INTO user_subscriptions (user_id, anchor_id)
    SELECT DISTINCT us.user_id, ak.anchor_id
    FROM user_subscriptions us
    JOIN anchors_to_delete ad ON us.anchor_id = ad.anchor_id
    JOIN anchors_to_keep ak ON ak.anchor_id = (
        SELECT anchor_id
        FROM same_name_anchors
        WHERE anchor_name = (SELECT anchor_name FROM anchors WHERE anchor_id = ad.anchor_id)
        ORDER BY rn
        LIMIT 1
    )
    ON CONFLICT (user_id, anchor_id) DO NOTHING;

    -- 删除anchors表中的重复条目
    DELETE FROM anchors
    WHERE anchor_id IN (SELECT anchor_id FROM anchors_to_delete);

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;