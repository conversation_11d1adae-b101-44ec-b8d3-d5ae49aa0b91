from typing import Union, Dict, Any
import re
import time
import json
from utils import (
    trace_error_decorator,
    update_config,
    dict_to_cookie_str
)
import http.cookiejar
import urllib.parse
import urllib.error
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from urllib.request import Request
from web_rid import get_live_room_id, get_sec_user_id

no_proxy_handler = urllib.request.ProxyHandler({})
opener = urllib.request.build_opener(no_proxy_handler)

def get_req(
    url: str,
    proxy_addr: Union[str, None] = None,
    headers: Union[dict, None] = None,
    data: Union[dict, bytes, None] = None,
    json_data: dict = None,
    timeout: int = 20,
    abroad: bool = False
) -> Union[str, Any]:

    if headers is None:
        headers = {}
    try:
        if proxy_addr:
            proxies = {
                'http': proxy_addr,
                'https': proxy_addr
            }
            if data or json_data:
                response = requests.post(url, data=data, json=json_data, headers=headers, proxies=proxies, timeout=timeout)
            else:
                response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            resp_str = response.text
        else:
            if data and not isinstance(data, bytes):
                data = urllib.parse.urlencode(data).encode('utf-8')
            if json_data and isinstance(json_data, dict):
                data = json.dumps(json_data).encode('utf-8')

            req = urllib.request.Request(url, data=data, headers=headers)

            try:
                if abroad:
                    with urllib.request.urlopen(req, timeout=timeout) as response:
                        resp_str = response.read().decode('utf-8')
                else:
                    with opener.open(req, timeout=timeout) as response:
                        resp_str = response.read().decode('utf-8')
            except urllib.error.HTTPError as e:
                if e.code == 400:
                    resp_str = e.read().decode('utf-8')
                else:
                    raise
            except urllib.error.URLError as e:
                print("URL Error:", e)
                raise
            except Exception as e:
                print("An error occurred:", e)
                raise

    except Exception as e:
        resp_str = str(e)

    return resp_str


# @trace_error_decorator
# def get_douyin_stream_data(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None, anchor_id: str = '') -> Dict[str, Any]:

#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
#         'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
#         'Referer': 'https://live.douyin.com/',
#         'Cookie': 'ttwid=1%7CB1qls3GdnZhUov9o2NxOMxxYS2ff6OSvEWbv0ytbES4%7C1680522049%7C280d802d6d478e3e78d0c807f7c487e7ffec0ae4e5fdd6a0fe74c3c6af149511; my_rd=1; passport_csrf_token=3ab34460fa656183fccfb904b16ff742; passport_csrf_token_default=3ab34460fa656183fccfb904b16ff742; d_ticket=9f562383ac0547d0b561904513229d76c9c21; n_mh=hvnJEQ4Q5eiH74-84kTFUyv4VK8xtSrpRZG1AhCeFNI; store-region=cn-fj; store-region-src=uid; LOGIN_STATUS=1; __security_server_data_status=1; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; pwa2=%223%7C0%7C3%7C0%22; download_guide=%223%2F20230729%2F0%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.6%7D; strategyABtestKey=%************.923%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A150%7D%22; VIDEO_FILTER_MEMO_SELECT=%7B%22expireTime%22%3A1691443863751%2C%22type%22%3Anull%7D; home_can_add_dy_2_desktop=%221%22; __live_version__=%221.1.1.2169%22; device_web_cpu_core=8; device_web_memory_size=8; xgplayer_user_id=************; csrf_session_id=2e00356b5cd8544d17a0e66484946f28; odin_tt=724eb4dd23bc6ffaed9a1571ac4c757ef597768a70c75fef695b95845b7ffcd8b1524278c2ac31c2587996d058e03414595f0a4e856c53bd0d5e5f56dc6d82e24004dc77773e6b83ced6f80f1bb70627; __ac_nonce=064caded4009deafd8b89; __ac_signature=_02B4Z6wo00f01HLUuwwAAIDBh6tRkVLvBQBy9L-AAHiHf7; ttcid=2e9619ebbb8449eaa3d5a42d8ce88ec835; webcast_leading_last_show_time=1691016922379; webcast_leading_total_show_times=1; webcast_local_quality=sd; live_can_add_dy_2_desktop=%221%22; msToken=1JDHnVPw_9yTvzIrwb7cQj8dCMNOoesXbA_IooV8cezcOdpe4pzusZE7NB7tZn9TBXPr0ylxmv-KMs5rqbNUBHP4P7VBFUu0ZAht_BEylqrLpzgt3y5ne_38hXDOX8o=; msToken=jV_yeN1IQKUd9PlNtpL7k5vthGKcHo0dEh_QPUQhr8G3cuYv-Jbb4NnIxGDmhVOkZOCSihNpA2kvYtHiTW25XNNX_yrsv5FN8O6zm3qmCIXcEe0LywLn7oBO2gITEeg=; tt_scid=mYfqpfbDjqXrIGJuQ7q-DlQJfUSG51qG.KUdzztuGP83OjuVLXnQHjsz-BRHRJu4e986'
#     }

#     if cookies:
#         headers['Cookie'] = cookies

#     try:
#         html_str = get_req(url=url, proxy_addr=proxy_addr, headers=headers)
#         match_json_str = re.search(r'(\{\\"state\\":.*?)]\\n"]\)', html_str)
#         if not match_json_str:
#             match_json_str = re.search(r'(\{\\"common\\":.*?)]\\n"]\)</script><div hidden', html_str)
#         json_str = match_json_str.group(1)
#         cleaned_string = json_str.replace('\\', '').replace(r'u0026', r'&')
#         room_store = re.search('"roomStore":(.*?),"linkmicStore"', cleaned_string, re.S).group(1)
#         anchor_name = re.search('"nickname":"(.*?)","avatar_thumb', room_store, re.S).group(1)
#         room_store = room_store.split(',"has_commerce_goods"')[0] + '}}}'
#         json_data = json.loads(room_store)['roomInfo']['room']
#         room_status = json_data['status']
#         status = True if room_status == 2 else False
#         title = ''
#         stream_url = ''

#         if status:
#             title = json_data['title']
#             stream_url_data = json_data['stream_url']
#             m3u8_url_list = stream_url_data['hls_pull_url_map']
#             quality_list: list = list(m3u8_url_list.keys())
#             quality_key = quality_list[0]  # 默认选择第一个可用的画质
#             stream_url = m3u8_url_list.get(quality_key)  # 直接使用获取到的URL，不进行修改

#         result = {
#             'anchor_id': anchor_id,
#             'new_url': url,
#             'anchor_name': anchor_name,
#             'status': status,
#             'title': title,
#             'stream_url': stream_url
#         }
#         return result

#     except Exception as e:
#         print(f"失败地址:{url} 准备切换解析方法, 异常信息: {e}")
#         web_rid = re.match('https://live.douyin.com/(\\d+)', url).group(1)
#         headers['Cookie'] = 'sessionid=69aba0873496d3eb8edcebb7baf32784'
#         url2 = f'https://live.douyin.com/webcast/room/web/enter/?aid=6383&app_name=douyin_web&live_id=1&device_platform=web&language=zh-CN&browser_language=zh-CN&browser_platform=Win32&browser_name=Chrome&browser_version=116.0.0.0&web_rid={web_rid}'
#         json_str = get_req(url=url2, proxy_addr=proxy_addr, headers=headers)
#         json_data = json.loads(json_str)
#         data = json_data.get('data', {})
#         user = data.get('user', {})
#         anchor_name = user.get('nickname', '')
#         status = data.get('room_status', 0) == 0

#         title = ''
#         stream_url = ''

#         if status:
#             stream_data = data['data'][0]
#             title = stream_data.get('title', '')
#             stream_url_data = stream_data.get('stream_url', {})
#             m3u8_url_list = stream_url_data.get('hls_pull_url_map', {})
#             quality_list = list(m3u8_url_list.keys())
#             quality_key = quality_list[0]  # 默认选择第一个可用的画质
#             stream_url = m3u8_url_list.get(quality_key)  # 直接使用获取到的URL，不进行修改
            
#         result = {
#             'anchor_id': anchor_id,
#             'new_url': url,
#             'anchor_name': anchor_name,
#             'status': status,
#             'title': title,
#             'stream_url': stream_url
#         }
#         return result

@trace_error_decorator
def get_tiktok_stream_data(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None, anchor_id: str = '') -> Dict[str, Any]:

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Edg/114.0.1823.79',
        'Cookie': 'ttwid=1%7CM-rF193sJugKuNz2RGNt-rh6pAAR9IMceUSzlDnPCNI%7C1683274418%7Cf726d4947f2fc37fecc7aeb0cdaee52892244d04efde6f8a8edd2bb168263269; tiktok_webapp_theme=light; tt_chain_token=VWkygAWDlm1cFg/k8whmOg==; passport_csrf_token=6e422c5a7991f8cec7033a8082921510; passport_csrf_token_default=6e422c5a7991f8cec7033a8082921510; d_ticket=f8c267d4af4523c97be1ccb355e9991e2ae06; odin_tt=320b5f386cdc23f347be018e588873db7f7aea4ea5d1813681c3fbc018ea025dde957b94f74146dbc0e3612426b865ccb95ec8abe4ee36cca65f15dbffec0deff7b0e69e8ea536d46e0f82a4fc37d211; cmpl_token=AgQQAPNSF-RO0rT04baWtZ0T_jUjl4fVP4PZYM2QPw; uid_tt=319b558dbba684bb1557206c92089cd113a875526a89aee30595925d804b81c7; uid_tt_ss=319b558dbba684bb1557206c92089cd113a875526a89aee30595925d804b81c7; sid_tt=ad5e736f4bedb2f6d42ccd849e706b1d; sessionid=ad5e736f4bedb2f6d42ccd849e706b1d; sessionid_ss=ad5e736f4bedb2f6d42ccd849e706b1d; store-idc=useast5; store-country-code=us; store-country-code-src=uid; tt-target-idc=useast5; tt-target-idc-sign=qXNk0bb1pDQ0FbCNF120Pl9WWMLZg9Edv5PkfyCbS4lIk5ieW5tfLP7XWROnN0mEaSlc5hg6Oji1pF-yz_3ZXnUiNMrA9wNMPvI6D9IFKKVmq555aQzwPIGHv0aQC5dNRgKo5Z5LBkgxUMWEojTKclq2_L8lBciw0IGdhFm_XyVJtbqbBKKgybGDLzK8ZyxF4Jl_cYRXaDlshZjc38JdS6wruDueRSHe7YvNbjxCnApEFUv-OwJANSPU_4rvcqpVhq3JI2VCCfw-cs_4MFIPCDOKisk5EhAo2JlHh3VF7_CLuv80FXg_7ZqQ2pJeMOog294rqxwbbQhl3ATvjQV_JsWyUsMd9zwqecpylrPvtySI2u1qfoggx1owLrrUynee1R48QlanLQnTNW_z1WpmZBgVJqgEGLwFoVOmRzJuFFNj8vIqdjM2nDSdWqX8_wX3wplohkzkPSFPfZgjzGnQX28krhgTytLt7BXYty5dpfGtsdb11WOFHM6MZ9R9uLVB; sid_guard=ad5e736f4bedb2f6d42ccd849e706b1d%7C1690990657%7C15525213%7CMon%2C+29-Jan-2024+08%3A11%3A10+GMT; sid_ucp_v1=1.0.0-KGM3YzgwYjZhODgyYWI1NjIwNTA0NjBmOWUxMGRhMjIzYTI2YjMxNDUKGAiqiJ30keKD5WQQwfCppgYYsws4AkDsBxAEGgd1c2Vhc3Q1IiBhZDVlNzM2ZjRiZWRiMmY2ZDQyY2NkODQ5ZTcwNmIxZA; ssid_ucp_v1=1.0.0-KGM3YzgwYjZhODgyYWI1NjIwNTA0NjBmOWUxMGRhMjIzYTI2YjMxNDUKGAiqiJ30keKD5WQQwfCppgYYsws4AkDsBxAEGgd1c2Vhc3Q1IiBhZDVlNzM2ZjRiZWRiMmY2ZDQyY2NkODQ5ZTcwNmIxZA; tt_csrf_token=dD0EIH8q-pe3qDQsCyyD1jLN6KizJDRjOEyk; __tea_cache_tokens_1988={%22_type_%22:%22default%22%2C%22user_unique_id%22:%227229608516049831425%22%2C%22timestamp%22:1683274422659}; ttwid=1%7CM-rF193sJugKuNz2RGNt-rh6pAAR9IMceUSzlDnPCNI%7C1694002151%7Cd89b77afc809b1a610661a9d1c2784d80ebef9efdd166f06de0d28e27f7e4efe; msToken=KfJAVZ7r9D_QVeQlYAUZzDFbc1Yx-nZz6GF33eOxgd8KlqvTg1lF9bMXW7gFV-qW4MCgUwnBIhbiwU9kdaSpgHJCk-PABsHCtTO5J3qC4oCTsrXQ1_E0XtbqiE4OVLZ_jdF1EYWgKNPT2SnwGkQ=; msToken=KfJAVZ7r9D_QVeQlYAUZzDFbc1Yx-nZz6GF33eOxgd8KlqvTg1lF9bMXW7gFV-qW4MCgUwnBIhbiwU9kdaSpgHJCk-PABsHCtTO5J3qC4oCTsrXQ1_E0XtbqiE4OVLZ_jdF1EYWgKNPT2SnwGkQ='
    }

    if cookies:
        headers['Cookie'] = cookies

    json_data = None
    for i in range(2):
        html_str = get_req(url=url, proxy_addr=proxy_addr, headers=headers, abroad=True)
        if 'UNEXPECTED_EOF_WHILE_READING' not in html_str:
            try:
                json_str = re.findall(
                    '<script id="SIGI_STATE" type="application/json">(.*?)</script>',
                    html_str)[0]
                json_data = json.loads(json_str)
            except Exception:
                raise ConnectionError("请检查你的网络是否可以正常访问TikTok网站")
        time.sleep(3)  # 每次循环前延迟3秒

    if json_data is None:
        raise ConnectionError("无法获取TikTok数据,请检查网络连接")

    return json_data

# def get_douyin_live_info(url: str, anchor_id: str = '') -> dict:
#     try:
#         if url.startswith('https://live.douyin.com/'):
#             douyin_url = url
#         elif url.startswith('https://v.douyin.com/'):
#             room_id, sec_user_id = get_sec_user_id(url)
#             web_rid = get_live_room_id(room_id, sec_user_id)
#             douyin_url = "https://live.douyin.com/" + str(web_rid)
#         else:
#             raise ValueError(f"不支持的抖音链接类型: {url}")

#         result = get_douyin_stream_data(douyin_url, anchor_id=anchor_id)
#         return result
#     except Exception as e:
#         print(f"解析抖音链接失败: {url}, 错误信息: {e}")
#         return {'error': str(e), 'url': url, 'anchor_id': anchor_id}

def get_tiktok_live_info(live_url, anchor_id: str = ''):
    """
    解析TikTok直播链接
    :param live_url: TikTok直播链接
    :param anchor_id: 主播ID,用于绑定数据
    :return: 解析后的链接、主播名字和直播状态
    """
    try:
        json_data = get_tiktok_stream_data(url=live_url, anchor_id=anchor_id)
        if isinstance(json_data, dict) and 'LiveRoom' in json_data:
            live_room = json_data['LiveRoom']['liveRoomUserInfo']
            unique_id = live_room['user']['uniqueId'] if 'user' in live_room else ''
            anchor_name = live_room['user']['nickname'] if 'user' in live_room else ''
            status = live_room['user']['status'] == 2 if 'user' in live_room else False
            title = live_room['liveRoom']['title'] if 'liveRoom' in live_room else ''
            url = f"https://www.tiktok.com/@{unique_id}/live"
            stream_info = get_tiktok_stream_url(json_data)
            stream_url = stream_info.get('record_url', '')

            result = {
                'anchor_id': anchor_id,
                'new_url': url,
                'anchor_name': anchor_name,
                'status': status,
                'title': title,
                'stream_url': stream_url
            }
            return result
        else:
            raise KeyError("'liveRoomUserInfo' not found")
    except KeyError as e:
        if str(e) == "'liveRoomUserInfo'":
            result = {
                'anchor_id': anchor_id,
                'anchor_name': 'NoLongerExisted',
                'new_url': live_url,
                'status': False,
            }
            # print(f"解析结果: {result}")
            return result
        else:
            raise e
    except Exception as e:
        logger.warning(f"解析TikTok链接失败: {live_url}, 错误信息: {e}")
        raise e


def get_tiktok_stream_url(json_data: dict, anchor_id: str = '') -> dict:
    if not json_data:
        return {"anchor_id": anchor_id, "anchor_name": None, "status": False}

    live_room = json_data['LiveRoom']['liveRoomUserInfo']
    user = live_room['user'] 
    anchor_name = f"{user['nickname']}-{user['uniqueId']}"
    status = user.get("status", 4)

    result = {
        "anchor_id": anchor_id,
        "anchor_name": anchor_name,
        "status": False,
    }

    if status == 2:
        stream_data = live_room.get('liveRoom', {}).get('streamData', {}).get('pull_data', {}).get('stream_data', '{}')
        stream_data = json.loads(stream_data).get('data', {})

        quality_order = ['origin', 'uhd', 'hd', 'sd']
        quality_list = sorted(stream_data.keys(), key=lambda x: quality_order.index(x) if x in quality_order else len(quality_order))

        for quality_key in quality_list:
            video_quality_urls = {
                'hls': stream_data[quality_key]['main']['hls'],  # 直接使用原始HLS URL
                'flv': stream_data[quality_key]['main']['flv'],  # 直接使用原始FLV URL
            }

            if video_quality_urls['flv']:
                try:
                    response = urllib.request.urlopen(video_quality_urls['flv'])
                    if response.status == 200 and response.read(3) == b'FLV':
                        result['flv_url'] = video_quality_urls['flv']
                        result['m3u8_url'] = video_quality_urls['hls']
                        result['status'] = True
                        result['record_url'] = result['flv_url']
                        break
                except:
                    pass

            if video_quality_urls['hls']:
                try:
                    response = urllib.request.urlopen(video_quality_urls['hls'])
                    if response.status == 200 and response.read(7) == b'#EXTM3U':
                        result['flv_url'] = video_quality_urls['flv']
                        result['m3u8_url'] = video_quality_urls['hls']
                        result['status'] = True
                        result['record_url'] = result['m3u8_url']
                        break  
                except:
                    pass

    return result


@trace_error_decorator
def get_douyin_stream_data(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None,
                           anchor_id: str = '') -> Dict[str, Any]:
    """
    处理 live.douyin.com 链接，获取直播间信息
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer': 'https://live.douyin.com/',
        'Cookie': 'ttwid=1%7CB1qls3GdnZhUov9o2NxOMxxYS2ff6OSvEWbv0ytbES4%7C1680522049%7C280d802d6d478e3e78d0c807f7c487e7ffec0ae4e5fdd6a0fe74c3c6af149511; my_rd=1; passport_csrf_token=3ab34460fa656183fccfb904b16ff742; passport_csrf_token_default=3ab34460fa656183fccfb904b16ff742; d_ticket=9f562383ac0547d0b561904513229d76c9c21; n_mh=hvnJEQ4Q5eiH74-84kTFUyv4VK8xtSrpRZG1AhCeFNI; store-region=cn-fj; store-region-src=uid; LOGIN_STATUS=1; __security_server_data_status=1; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; pwa2=%223%7C0%7C3%7C0%22; download_guide=%223%2F20230729%2F0%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.6%7D; strategyABtestKey=%************.923%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A150%7D%22; VIDEO_FILTER_MEMO_SELECT=%7B%22expireTime%22%3A1691443863751%2C%22type%22%3Anull%7D; home_can_add_dy_2_desktop=%221%22; __live_version__=%221.1.1.2169%22; device_web_cpu_core=8; device_web_memory_size=8; xgplayer_user_id=************; csrf_session_id=2e00356b5cd8544d17a0e66484946f28; odin_tt=724eb4dd23bc6ffaed9a1571ac4c757ef597768a70c75fef695b95845b7ffcd8b1524278c2ac31c2587996d058e03414595f0a4e856c53bd0d5e5f56dc6d82e24004dc77773e6b83ced6f80f1bb70627; __ac_nonce=064caded4009deafd8b89; __ac_signature=_02B4Z6wo00f01HLUuwwAAIDBh6tRkVLvBQBy9L-AAHiHf7; ttcid=2e9619ebbb8449eaa3d5a42d8ce88ec835; webcast_leading_last_show_time=1691016922379; webcast_leading_total_show_times=1; webcast_local_quality=sd; live_can_add_dy_2_desktop=%221%22; msToken=1JDHnVPw_9yTvzIrwb7cQj8dCMNOoesXbA_IooV8cezcOdpe4pzusZE7NB7tZn9TBXPr0ylxmv-KMs5rqbNUBHP4P7VBFUu0ZAht_BEylqrLpzgt3y5ne_38hXDOX8o=; msToken=jV_yeN1IQKUd9PlNtpL7k5vthGKcHo0dEh_QPUQhr8G3cuYv-Jbb4NnIxGDmhVOkZOCSihNpA2kvYtHiTW25XNNX_yrsv5FN8O6zm3qmCIXcEe0LywLn7oBO2gITEeg=; tt_scid=mYfqpfbDjqXrIGJuQ7q-DlQJfUSG51qG.KUdzztuGP83OjuVLXnQHjsz-BRHRJu4e986'
    }
    
    if cookies:
        headers['Cookie'] = cookies

    try:
        html_str = get_req(url=url, proxy_addr=proxy_addr, headers=headers)
        match_json_str = re.search(r'(\{\\"state\\":.*?)]\\n"]\)', html_str)
        if not match_json_str:
            match_json_str = re.search(r'(\{\\"common\\":.*?)]\\n"]\)</script><div hidden', html_str)
        json_str = match_json_str.group(1)
        cleaned_string = json_str.replace('\\', '').replace(r'u0026', r'&')
        room_store = re.search('"roomStore":(.*?),"linkmicStore"', cleaned_string, re.S).group(1)
        anchor_name = re.search('"nickname":"(.*?)","avatar_thumb', room_store, re.S).group(1)
        room_store = room_store.split(',"has_commerce_goods"')[0] + '}}}'
        json_data = json.loads(room_store)['roomInfo']['room']
        room_status = json_data['status']
        status = True if room_status == 2 else False
        title = ''
        stream_url = ''
    
        if status:
            title = json_data['title']
            stream_url_data = json_data['stream_url']
            m3u8_url_list = stream_url_data['hls_pull_url_map']
            quality_list: list = list(m3u8_url_list.keys())
            quality_key = quality_list[0]  # 默认选择第一个可用的画质
            stream_url = m3u8_url_list.get(quality_key)
    
        result = {
            'anchor_id': anchor_id,
            'new_url': url,
            'anchor_name': anchor_name,
            'status': status,
            'title': title,
            'stream_url': stream_url
        }
        return result
    
    except Exception as e:
        print(f"解析 live.douyin.com 链接失败: {url}, 错误信息: {e}")
        return {'error': str(e), 'url': url, 'anchor_id': anchor_id}


@trace_error_decorator
def get_douyin_app_stream_data(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None,
                               anchor_id: str = '') -> Dict[str, Any]:
    """
    处理 v.douyin.com 链接，获取直播间信息，并返回对应的 live.douyin.com 长链接
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer': 'https://live.douyin.com/',
        'Cookie': 'ttwid=1%7CB1qls3GdnZhUov9o2NxOMxxYS2ff6OSvEWbv0ytbES4%7C1680522049%7C280d802d6d478e3e78d0c807f7c487e7ffec0ae4e5fdd6a0fe74c3c6af149511; my_rd=1; passport_csrf_token=3ab34460fa656183fccfb904b16ff742; passport_csrf_token_default=3ab34460fa656183fccfb904b16ff742; d_ticket=9f562383ac0547d0b561904513229d76c9c21; n_mh=hvnJEQ4Q5eiH74-84kTFUyv4VK8xtSrpRZG1AhCeFNI; store-region=cn-fj; store-region-src=uid; LOGIN_STATUS=1; __security_server_data_status=1; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; pwa2=%223%7C0%7C3%7C0%22; download_guide=%223%2F20230729%2F0%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.6%7D; strategyABtestKey=%************.923%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A150%7D%22; VIDEO_FILTER_MEMO_SELECT=%7B%22expireTime%22%3A1691443863751%2C%22type%22%3Anull%7D; home_can_add_dy_2_desktop=%221%22; __live_version__=%221.1.1.2169%22; device_web_cpu_core=8; device_web_memory_size=8; xgplayer_user_id=************; csrf_session_id=2e00356b5cd8544d17a0e66484946f28; odin_tt=724eb4dd23bc6ffaed9a1571ac4c757ef597768a70c75fef695b95845b7ffcd8b1524278c2ac31c2587996d058e03414595f0a4e856c53bd0d5e5f56dc6d82e24004dc77773e6b83ced6f80f1bb70627; __ac_nonce=064caded4009deafd8b89; __ac_signature=_02B4Z6wo00f01HLUuwwAAIDBh6tRkVLvBQBy9L-AAHiHf7; ttcid=2e9619ebbb8449eaa3d5a42d8ce88ec835; webcast_leading_last_show_time=1691016922379; webcast_leading_total_show_times=1; webcast_local_quality=sd; live_can_add_dy_2_desktop=%221%22; msToken=1JDHnVPw_9yTvzIrwb7cQj8dCMNOoesXbA_IooV8cezcOdpe4pzusZE7NB7tZn9TBXPr0ylxmv-KMs5rqbNUBHP4P7VBFUu0ZAht_BEylqrLpzgt3y5ne_38hXDOX8o=; msToken=jV_yeN1IQKUd9PlNtpL7k5vthGKcHo0dEh_QPUQhr8G3cuYv-Jbb4NnIxGDmhVOkZOCSihNpA2kvYtHiTW25XNNX_yrsv5FN8O6zm3qmCIXcEe0LywLn7oBO2gITEeg=; tt_scid=mYfqpfbDjqXrIGJuQ7q-DlQJfUSG51qG.KUdzztuGP83OjuVLXnQHjsz-BRHRJu4e986'
    }
    
    if cookies:
        headers['Cookie'] = cookies

    try:
        # 获取 room_id 和 sec_user_id
        room_id, sec_uid = get_sec_user_id(url=url, proxy_addr=proxy_addr)
        
        # 获取 live.douyin.com 的 web_rid
        web_rid = get_live_room_id(room_id, sec_uid, proxy_addr=proxy_addr)
        
        # 构造新的 live.douyin.com 链接
        new_url = f"https://live.douyin.com/{web_rid}"
        
        # 使用新的链接获取直播间信息
        api_url = f'https://webcast.amemv.com/webcast/room/reflow/info/?verifyFp=verify_xxx&type_id=0&live_id=1&room_id={room_id}&sec_user_id={sec_uid}&version_code=99.99.99&app_id=1128'
        json_str = get_req(url=api_url, proxy_addr=proxy_addr, headers=headers)
        json_data = json.loads(json_str)['data']
        room_data = json_data['room']
        anchor_name = room_data['owner']['nickname']
        room_status = room_data['status']
        status = True if room_status == 2 else False
        title = ''
        stream_url = ''
        
        if status:
            title = room_data['title']
            stream_url_data = room_data['stream_url']
            m3u8_url_list = stream_url_data['hls_pull_url_map']
            quality_list: list = list(m3u8_url_list.keys())
            quality_key = quality_list[0]  # 默认选择第一个可用的画质
            stream_url = m3u8_url_list.get(quality_key)
        
        result = {
            'anchor_id': anchor_id,
            'new_url': new_url,  # 返回 live.douyin.com 的链接
            'anchor_name': anchor_name,
            'status': status,
            'title': title,
            'stream_url': stream_url
        }
        return result

    except Exception as e:
        print(f"解析 v.douyin.com 链接失败: {url}, 错误信息: {e}")
        return {'error': str(e), 'url': url, 'anchor_id': anchor_id}



def get_douyin_live_info(url: str, anchor_id: str = '') -> dict:
    try:
        if url.startswith('https://live.douyin.com/'):
            result = get_douyin_stream_data(url, anchor_id=anchor_id)
        elif url.startswith('https://v.douyin.com/'):
            result = get_douyin_app_stream_data(url, anchor_id=anchor_id)
        else:
            raise ValueError(f"不支持的抖音链接类型: {url}")
        return result
    except Exception as e:
        print(f"解析抖音链接失败: {url}, 错误信息: {e}")
        return {'error': str(e), 'url': url, 'anchor_id': anchor_id}
