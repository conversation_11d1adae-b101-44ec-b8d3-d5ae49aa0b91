#!/usr/bin/env python3
"""
创建 Telegram session 文件
"""
import asyncio
from pyrogram import Client

# Telegram 配置
API_ID = 26615736
API_HASH = '5a9ab0c939e69a433f039b693e3797f5'
SESSION_NAME = 'finka_uploader'

async def main():
    """创建 session"""
    app = Client(
        SESSION_NAME,
        api_id=API_ID,
        api_hash=API_HASH
    )
    
    async with app:
        me = await app.get_me()
        print(f"✅ Session 创建成功!")
        print(f"👤 用户名: {me.username}")
        print(f"📱 电话: {me.phone_number}")
        print(f"🆔 用户ID: {me.id}")

if __name__ == "__main__":
    print("请输入你的 Telegram 账号信息来创建 session 文件")
    print("=" * 50)
    asyncio.run(main())