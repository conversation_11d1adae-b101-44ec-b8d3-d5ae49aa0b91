import psycopg2
from psycopg2 import pool

DB_PARAMS = {
    'host': 'aws-0-ap-northeast-1.pooler.supabase.com',
    'port': 5432,
    'dbname': 'postgres',
    'user': 'postgres.nathkbbkyfjthxpdqocu',
    'password': '4gyp2MJp84zxW.F',
    'minconn': 2,
    'maxconn': 30
}

class Singleton(type):
    _instances = {}
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

class DBUtils(metaclass=Singleton):
    def __init__(self):
        self.pool = None

    def __getattr__(self, name):
        if self.pool is None:
            self.pool = psycopg2.pool.SimpleConnectionPool(
                minconn=DB_PARAMS['minconn'],
                maxconn=DB_PARAMS['maxconn'],
                host=DB_PARAMS['host'],
                port=DB_PARAMS['port'],
                dbname=DB_PARAMS['dbname'],
                user=DB_PARAMS['user'],
                password=DB_PARAMS['password'],
                connect_timeout=60
            )
        return getattr(self, name)

    def __enter__(self):
        self.conn = self.get_conn()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.conn.close()

    def get_conn(self):
        return self.pool.getconn()

    def update_mp4id(self, mp4id, anchor_id):
        conn = self.get_conn()
        try:
            update_query = "UPDATE anchors SET mp4id = %s WHERE anchor_id = %s"
            with conn.cursor() as cursor:
                cursor.execute(update_query, (mp4id, anchor_id))
            conn.commit()
        finally:
            conn.close()

    def update_tsid(self, tsid, anchor_id):
        conn = self.get_conn()
        try:
            update_query = "UPDATE anchors SET tsid = %s WHERE anchor_id = %s"
            with conn.cursor() as cursor:
                cursor.execute(update_query, (tsid, anchor_id))
            conn.commit()
        finally:
            conn.close()

    def close_pool(self):
        if self.pool:
            self.pool.closeall()