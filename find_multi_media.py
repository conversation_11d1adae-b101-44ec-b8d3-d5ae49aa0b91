#!/usr/bin/env python3
"""
查找包含多个媒体文件的作品
"""

import json
import time
import logging
from finka import FinkaAPI, Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def find_multi_media_posts():
    """查找包含多个媒体文件的作品"""
    api = FinkaAPI()
    
    logger.info("开始查找包含多媒体的作品...")
    
    # 获取关注列表
    result = api.get_following_list(count=30)
    
    if not result or not result.get("users"):
        logger.error("无法获取关注用户列表")
        return None
    
    users = result["users"]
    logger.info(f"获取到 {len(users)} 个用户")
    
    multi_media_posts = []
    
    # 遍历用户
    for idx, user in enumerate(users, 1):
        user_id = user.get('id', user.get('userId', ''))
        user_name = user.get('name', '未知')
        
        logger.info(f"[{idx}/{len(users)}] 检查用户: {user_name} (ID: {user_id})")
        
        # 获取用户作品
        posts_result = api.get_user_posts(user_id, count=30)
        
        if posts_result and posts_result.get("success") and posts_result.get("data"):
            data = posts_result["data"]
            posts = data.get("list", [])
            
            for post in posts:
                post_id = post.get('postId', post.get('id', ''))
                post_type = post.get('type', '')
                
                # 检查是否有多个图片
                images = post.get('images', [])
                image = post.get('image', {})
                
                # 检查是否有多个视频
                videos = post.get('videos', [])
                video = post.get('video', {})
                
                # 判断是否是多媒体作品
                is_multi_media = False
                media_count = 0
                media_info = []
                
                if images and isinstance(images, list) and len(images) > 1:
                    is_multi_media = True
                    media_count = len(images)
                    media_info = [{'type': 'image', 'id': img.get('imageId', ''), 'url': f"https://pic1.finkapp.cn/{img.get('imageId', '')}"} for img in images]
                    logger.info(f"  ✅ 发现多图片作品: {post_id}, 包含 {media_count} 张图片")
                
                elif videos and isinstance(videos, list) and len(videos) > 1:
                    is_multi_media = True
                    media_count = len(videos)
                    media_info = [{'type': 'video', 'id': vid.get('videoId', ''), 'url': vid.get('url', '')} for vid in videos]
                    logger.info(f"  ✅ 发现多视频作品: {post_id}, 包含 {media_count} 个视频")
                
                # 检查描述中是否提到多张图片
                description = post.get('description', '')
                if '多张图片' in description or '多个视频' in description:
                    logger.info(f"  📝 作品 {post_id} 描述提到多媒体: {description[:50]}...")
                    
                    # 即使API只返回一个图片，也记录下来
                    if not is_multi_media and image.get('imageId'):
                        media_info = [{
                            'type': 'single_image_but_has_more',
                            'id': image.get('imageId', ''),
                            'url': f"https://pic1.finkapp.cn/{image.get('imageId', '')}",
                            'note': '描述提到多张图片但API只返回一张'
                        }]
                
                if is_multi_media or media_info:
                    multi_media_posts.append({
                        'user_id': user_id,
                        'user_name': user_name,
                        'post_id': post_id,
                        'post_type': post_type,
                        'description': description,
                        'media_count': media_count if media_count > 0 else 1,
                        'media_info': media_info,
                        'raw_data': post
                    })
                    
                    # 如果找到真正的多媒体作品，立即返回
                    if is_multi_media:
                        logger.info(f"\n🎉 找到包含 {media_count} 个媒体文件的作品！")
                        logger.info(f"作者: {user_name}")
                        logger.info(f"作品ID: {post_id}")
                        logger.info(f"媒体类型: {post_type}")
                        logger.info(f"描述: {description[:100]}...")
                        
                        # 保存详细信息
                        with open('multi_media_post.json', 'w', encoding='utf-8') as f:
                            json.dump({
                                'user_id': user_id,
                                'user_name': user_name,
                                'post_id': post_id,
                                'post_type': post_type,
                                'description': description,
                                'media_count': media_count,
                                'media_info': media_info,
                                'raw_data': post
                            }, f, ensure_ascii=False, indent=2)
                        
                        logger.info("详细信息已保存到 multi_media_post.json")
                        return multi_media_posts
        
        time.sleep(1)  # 避免请求过快
        
        # 如果已经找到一些候选作品，可以提前结束
        if len(multi_media_posts) >= 5:
            break
    
    # 输出结果
    if multi_media_posts:
        logger.info(f"\n共找到 {len(multi_media_posts)} 个可能的多媒体作品")
        
        # 保存所有结果
        with open('all_multi_media_posts.json', 'w', encoding='utf-8') as f:
            json.dump(multi_media_posts, f, ensure_ascii=False, indent=2)
        
        logger.info("所有结果已保存到 all_multi_media_posts.json")
    else:
        logger.warning("未找到包含多个媒体文件的作品")
    
    return multi_media_posts


if __name__ == "__main__":
    posts = find_multi_media_posts()
    
    if posts:
        logger.info(f"\n找到 {len(posts)} 个作品")
        for post in posts[:3]:  # 显示前3个
            logger.info(f"\n作品: {post['post_id']}")
            logger.info(f"  作者: {post['user_name']}")
            logger.info(f"  媒体数量: {post['media_count']}")
            logger.info(f"  描述: {post['description'][:50]}...")