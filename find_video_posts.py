#!/usr/bin/env python3
"""
查找视频作品
"""

import json
import time
import logging
from finka import FinkaAPI, Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def find_video_posts():
    """查找视频作品"""
    api = FinkaAPI()
    
    logger.info("开始查找视频作品...")
    
    # 获取关注列表
    result = api.get_following_list(count=30)
    
    if not result or not result.get("users"):
        logger.error("无法获取关注用户列表")
        return None
    
    users = result["users"]
    logger.info(f"获取到 {len(users)} 个用户")
    
    video_posts = []
    
    # 遍历用户
    for idx, user in enumerate(users, 1):
        user_id = user.get('id', user.get('userId', ''))
        user_name = user.get('name', '未知')
        
        logger.info(f"[{idx}/{len(users)}] 检查用户: {user_name} (ID: {user_id})")
        
        # 获取用户作品
        posts_result = api.get_user_posts(user_id, count=50)
        
        if posts_result and posts_result.get("success") and posts_result.get("data"):
            data = posts_result["data"]
            posts = data.get("list", [])
            
            for post in posts:
                post_id = post.get('postId', post.get('id', ''))
                post_type = post.get('type', '')
                
                if post_type == 'VideoPost':
                    video = post.get('video', {})
                    videos = post.get('videos', [])
                    
                    video_info = {
                        'user_id': user_id,
                        'user_name': user_name,
                        'post_id': post_id,
                        'post_type': post_type,
                        'description': post.get('description', ''),
                        'has_single_video': bool(video.get('url')),
                        'has_multiple_videos': bool(videos and len(videos) > 0),
                        'video_count': len(videos) if videos else (1 if video.get('url') else 0),
                        'video_url': video.get('url', ''),
                        'video_data': video,
                        'videos_data': videos,
                        'raw_data': post
                    }
                    
                    video_posts.append(video_info)
                    
                    logger.info(f"  ✅ 发现视频作品: {post_id}")
                    logger.info(f"     视频数量: {video_info['video_count']}")
                    logger.info(f"     描述: {video_info['description'][:50]}...")
                    
                    if video_info['has_multiple_videos']:
                        logger.info(f"  🎉 发现包含多个视频的作品！")
                        
                        # 保存第一个多视频作品
                        with open('multi_video_post.json', 'w', encoding='utf-8') as f:
                            json.dump(video_info, f, ensure_ascii=False, indent=2)
                        
                        logger.info("多视频作品已保存到 multi_video_post.json")
                        return video_posts
        
        time.sleep(1)  # 避免请求过快
        
        # 如果已经找到足够的视频作品
        if len(video_posts) >= 10:
            break
    
    # 保存所有视频作品
    if video_posts:
        logger.info(f"\n共找到 {len(video_posts)} 个视频作品")
        
        with open('all_video_posts.json', 'w', encoding='utf-8') as f:
            json.dump(video_posts, f, ensure_ascii=False, indent=2)
        
        logger.info("所有视频作品已保存到 all_video_posts.json")
    else:
        logger.warning("未找到视频作品")
    
    return video_posts


if __name__ == "__main__":
    posts = find_video_posts()
    
    if posts:
        logger.info(f"\n找到 {len(posts)} 个视频作品")
        for post in posts[:3]:  # 显示前3个
            logger.info(f"\n作品: {post['post_id']}")
            logger.info(f"  作者: {post['user_name']}")
            logger.info(f"  视频数量: {post['video_count']}")
            logger.info(f"  有单个视频: {post['has_single_video']}")
            logger.info(f"  有多个视频: {post['has_multiple_videos']}")
            logger.info(f"  描述: {post['description'][:30]}...")