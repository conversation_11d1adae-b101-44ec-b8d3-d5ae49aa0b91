#!/usr/bin/env python3
"""
Finka 完整下载工具 - 单文件版本
包含所有配置和功能，无需额外依赖文件
"""

import os
import time
import json
import hashlib
import requests
import asyncio
from datetime import datetime
from urllib.parse import urlencode
from typing import List, Dict, Optional, Tuple, Any, Union
import logging
from supabase import create_client, Client
from pyrogram import Client as TelegramClient
from pyrogram.types import InputMediaPhoto, InputMediaVideo


# ==================== 配置部分 ====================
class Config:
    """所有配置参数"""
    # API 认证配置
    AUTH_TOKEN = os.getenv('FINKA_AUTH_TOKEN', "aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY")
    
    # 下载目录配置
    DOWNLOAD_DIR = os.getenv('FINKA_DOWNLOAD_DIR', "/home/<USER>/downloader/downloads")
    
    # Telegram 配置
    TELEGRAM_API_ID = os.getenv('TELEGRAM_API_ID', '25432929')
    TELEGRAM_API_HASH = os.getenv('TELEGRAM_API_HASH', '965c5d22f0b9d1d0326e84bbb2bb18c1')
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
    TELEGRAM_CHANNEL_ID = os.getenv('TELEGRAM_CHANNEL_ID', 235196660)  # 发送到用户而不是频道
    TELEGRAM_SESSION_NAME = os.getenv('TELEGRAM_SESSION_NAME', 'finka_bot')
    
    # Supabase 配置
    SUPABASE_URL = os.getenv('SUPABASE_URL', 'https://wjanjmsywbydjbfrdkaz.supabase.co')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE')
    
    # API 配置
    API_BASE_URL = "https://api.finka.cn"
    IMAGE_BASE_URL = "https://pic1.finkapp.cn"
    VIDEO_DOMAIN = "vid1.finkapp.cn"
    
    # API 版本
    APP_VERSION = os.getenv('FINKA_APP_VERSION', '8.12.0')
    
    # 分页配置
    FOLLOWING_LIST_PAGE_SIZE = int(os.getenv('FINKA_FOLLOWING_PAGE_SIZE', '30'))
    USER_POSTS_PAGE_SIZE = int(os.getenv('FINKA_POSTS_PAGE_SIZE', '21'))
    MAX_PAGES_PER_USER = int(os.getenv('FINKA_MAX_PAGES', '100'))
    
    # 请求配置
    REQUEST_TIMEOUT = int(os.getenv('FINKA_REQUEST_TIMEOUT', '30'))
    DOWNLOAD_CHUNK_SIZE = int(os.getenv('FINKA_CHUNK_SIZE', '8192'))
    
    # 速率限制配置
    REQUEST_DELAY = float(os.getenv('FINKA_REQUEST_DELAY', '0.5'))
    USER_PROCESS_DELAY = float(os.getenv('FINKA_USER_DELAY', '2'))
    PAGE_REQUEST_DELAY = float(os.getenv('FINKA_PAGE_DELAY', '0.5'))
    
    # 重试配置
    MAX_RETRIES = int(os.getenv('FINKA_MAX_RETRIES', '3'))
    RETRY_DELAY = float(os.getenv('FINKA_RETRY_DELAY', '1'))
    
    # 文件命名配置
    FILE_DATE_FORMAT = '%Y%m%d_%H%M%S'
    
    # 日志配置
    LOG_LEVEL = os.getenv('FINKA_LOG_LEVEL', 'INFO')
    
    # 业务逻辑配置
    CONSECUTIVE_EMPTY_PAGES_LIMIT = int(os.getenv('FINKA_EMPTY_PAGES_LIMIT', '3'))
    SHOW_DOWNLOAD_PROGRESS = os.getenv('FINKA_SHOW_PROGRESS', 'true').lower() == 'true'


# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


# ==================== API 客户端 ====================
class FinkaAPI:
    """Finka API 客户端"""
    
    def __init__(self, auth_token: str = None):
        """初始化 Finka API 客户端"""
        self.auth_token = auth_token or Config.AUTH_TOKEN
        parts = self.auth_token.split('|')
        if len(parts) >= 2:
            self.access_token = parts[0]
            self.user_id = parts[1]
            self.refresh_token = parts[2] if len(parts) > 2 else ""
        else:
            raise ValueError("Invalid auth token format")
        
        self.base_url = Config.API_BASE_URL
        self.session = requests.Session()
        self._setup_headers()
    
    def _setup_headers(self):
        """设置请求头"""
        self.session.headers.update({
            'X-App-Auth': self.auth_token,
            'X-App-Version': Config.APP_VERSION,
            'X-App-Device': f'Finka0a%2F{Config.APP_VERSION}+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+250725002%2FOfficial%3B%29',
            'X-App-GUID': '484837FB-D8BD-4312-A893-8F04B4EE994A',
            'X-App-User': self.user_id,
            'Android-Store': 'Official',
            'Accept-Language': 'en-US',
            'User-Agent': f'Finka/{Config.APP_VERSION} (Android 12; Google Pixel 3 XL)',
            'Accept-Encoding': 'gzip',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })
    
    def _generate_digest(self, method: str, path: str, params: dict, body: str = "") -> str:
        """生成请求的 MD5 摘要"""
        query_string = ""
        if params:
            sorted_params = sorted(params.items())
            query_string = urlencode(sorted_params)
        
        sign_string = f"{method.upper()}{path}"
        if query_string:
            sign_string += f"?{query_string}"
        if body:
            sign_string += body
        
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def request(self, method: str, path: str, params: Optional[dict] = None, data: Optional[dict] = None) -> Optional[dict]:
        """发送 API 请求"""
        if params is None:
            params = {}
        params['_t'] = str(int(time.time() * 1000))
        
        body_string = ""
        if data:
            body_string = urlencode(data) if isinstance(data, dict) else data
        
        digest = self._generate_digest(method, path, params, body_string)
        url = self.base_url + path
        headers = {'Digest': digest}
        
        for attempt in range(Config.MAX_RETRIES):
            try:
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, headers=headers, timeout=Config.REQUEST_TIMEOUT)
                else:
                    response = self.session.post(url, params=params, data=body_string, headers=headers, timeout=Config.REQUEST_TIMEOUT)
                
                response.raise_for_status()
                return response.json()
            except Exception as e:
                if attempt < Config.MAX_RETRIES - 1:
                    logger.warning(f"API 请求失败 (尝试 {attempt + 1}/{Config.MAX_RETRIES}): {e}")
                    time.sleep(Config.RETRY_DELAY)
                else:
                    logger.error(f"API 请求失败: {e}")
                    return None
    
    def get_following_list(self, count: int = None, last_id: str = "") -> Optional[dict]:
        """获取关注列表"""
        count = count or Config.FOLLOWING_LIST_PAGE_SIZE
        data = {"count": str(count)}
        if last_id:
            data["lastId"] = last_id
        
        result = self.request("POST", "/user/match/liked/v3/active/v3", data=urlencode(data))
        
        if result and result.get("success") and result.get("data"):
            users = result["data"].get("list", [])
            has_more = result["data"].get("hasMore", 0)
            next_last_id = result["data"].get("lastId", "")
            
            return {
                "users": users,
                "hasMore": has_more,
                "lastId": next_last_id
            }
        return None
    
    def get_user_posts(self, user_id: str, count: int = None, cursor: str = "") -> Optional[dict]:
        """获取用户作品列表"""
        count = count or Config.USER_POSTS_PAGE_SIZE
        params = {
            "count": str(count),
            "uid": user_id
        }
        if cursor:
            params["cursor"] = cursor
        
        return self.request("GET", "/feed/user/v3", params)


# ==================== 下载器 ====================
class FinkaDownloader:
    """Finka 内容下载器"""
    
    def __init__(self, base_dir: str = None):
        """初始化下载器"""
        self.base_dir = base_dir or Config.DOWNLOAD_DIR
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://www.finkapp.cn/'
        })
        
        # 创建基础目录
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir)
    
    def _get_user_dir(self, user_name: str, user_id: str) -> str:
        """获取用户专属目录"""
        safe_name = "".join(c for c in user_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        user_dir = os.path.join(self.base_dir, f"{safe_name}_{user_id}")
        if not os.path.exists(user_dir):
            os.makedirs(user_dir)
        return user_dir
    
    def download_image(self, image_id: str, save_path: str, original: bool = True) -> bool:
        """下载图片
        
        Args:
            image_id: 图片ID
            save_path: 保存路径
            original: 是否下载原图（True）或使用压缩版本（False）
        """
        # 原图链接：直接使用imageId，不带任何参数
        # 注意：Finka的原图是直接访问imageId，不加任何参数
        if original:
            url = f"{Config.IMAGE_BASE_URL}/{image_id}"
            logger.info(f"下载原图: {url}")
        else:
            # 如果需要压缩版本，可以使用imageMogr2参数
            # 例如：?imageMogr2/quality/100 保持100%质量
            url = f"{Config.IMAGE_BASE_URL}/{image_id}?imageMogr2/quality/100"
            logger.info(f"下载高质量图片: {url}")
        
        for attempt in range(Config.MAX_RETRIES):
            try:
                response = self.session.get(url, timeout=Config.REQUEST_TIMEOUT)
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        f.write(response.content)
                    logger.info(f"✅ 图片下载成功: {save_path}")
                    return True
                else:
                    logger.error(f"❌ 图片下载失败: HTTP {response.status_code}")
                    if attempt < Config.MAX_RETRIES - 1:
                        time.sleep(Config.RETRY_DELAY)
            except Exception as e:
                if attempt < Config.MAX_RETRIES - 1:
                    logger.warning(f"图片下载错误 (尝试 {attempt + 1}/{Config.MAX_RETRIES}): {e}")
                    time.sleep(Config.RETRY_DELAY)
                else:
                    logger.error(f"❌ 图片下载错误: {e}")
        return False
    
    def download_video(self, video_url: str, save_path: str, show_progress: bool = None) -> bool:
        """下载视频"""
        show_progress = show_progress if show_progress is not None else Config.SHOW_DOWNLOAD_PROGRESS
        
        for attempt in range(Config.MAX_RETRIES):
            try:
                response = self.session.get(video_url, stream=True, timeout=Config.REQUEST_TIMEOUT)
                if response.status_code == 200:
                    total_size = int(response.headers.get('Content-Length', 0))
                    downloaded = 0
                    
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=Config.DOWNLOAD_CHUNK_SIZE):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                
                                if show_progress and total_size > 0:
                                    progress = downloaded / total_size * 100
                                    print(f"\r  下载进度: {progress:.1f}% ({downloaded/1024/1024:.1f}/{total_size/1024/1024:.1f} MB)", end='')
                    
                    if show_progress:
                        print()  # 换行
                    logger.info(f"✅ 视频下载成功: {save_path}")
                    return True
                else:
                    logger.error(f"❌ 视频下载失败: HTTP {response.status_code}")
                    if attempt < Config.MAX_RETRIES - 1:
                        time.sleep(Config.RETRY_DELAY)
            except Exception as e:
                if attempt < Config.MAX_RETRIES - 1:
                    logger.warning(f"视频下载错误 (尝试 {attempt + 1}/{Config.MAX_RETRIES}): {e}")
                    time.sleep(Config.RETRY_DELAY)
                else:
                    logger.error(f"❌ 视频下载错误: {e}")
        return False
    
    def download_post_content(self, post: dict, user_dir: str, author_id: str = None) -> Dict[str, str]:
        """下载作品的原始内容"""
        post_id = post.get('postId', post.get('id', ''))
        post_type = post.get('type', '')
        create_time = post.get('createTimeMillis', post.get('createTime', 0))
        
        # 获取作者ID
        if not author_id:
            user_data = post.get('user', {})
            author_id = user_data.get('id', user_data.get('userId', 'unknown'))
        
        # 使用 finka_作者ID_时间戳_作品ID 作为文件名前缀
        timestamp = datetime.fromtimestamp(create_time / 1000).strftime(Config.FILE_DATE_FORMAT) if create_time else 'unknown'
        file_prefix = f"finka_{author_id}_{timestamp}_{post_id}"
        
        downloaded_files = {}
        
        if post_type == 'ImagePost':
            # 优先检查imageInfoList（多图片）
            image_info_list = post.get('imageInfoList', [])
            if image_info_list and isinstance(image_info_list, list) and len(image_info_list) > 1:
                # 多张图片
                logger.info(f"  下载{len(image_info_list)}张图片...")
                image_paths = []
                for idx, info in enumerate(image_info_list):
                    if isinstance(info, dict) and 'image' in info:
                        img = info['image']
                        image_id = img.get('imageId')
                        if image_id:
                            # 多图片使用索引命名
                            image_path = os.path.join(user_dir, f"{file_prefix}_{idx+1}.jpg")
                            # 下载原图（original=True）
                            if self.download_image(image_id, image_path, original=True):
                                image_paths.append(image_path)
                                logger.info(f"    图片{idx+1}/{len(image_info_list)}: {img.get('width', 'N/A')}x{img.get('height', 'N/A')}")
                downloaded_files['images'] = image_paths
            else:
                # 单张图片（从imageInfoList或image字段获取）
                if image_info_list and len(image_info_list) == 1:
                    # 从imageInfoList获取单张图片
                    info = image_info_list[0]
                    if isinstance(info, dict) and 'image' in info:
                        image_data = info['image']
                else:
                    # 从image字段获取
                    image_data = post.get('image', {})
                
                image_id = image_data.get('imageId')
                if image_id:
                    # 单图片不带索引
                    image_path = os.path.join(user_dir, f"{file_prefix}.jpg")
                    # 下载原图（original=True）
                    if self.download_image(image_id, image_path, original=True):
                        downloaded_files['image'] = image_path
                        # 记录图片尺寸信息
                        logger.info(f"  原图尺寸: {image_data.get('width', 'N/A')}x{image_data.get('height', 'N/A')}")
        
        elif post_type == 'VideoPost':
            # 下载视频
            video_data = post.get('video', {})
            video_url = video_data.get('url')
            
            if video_url:
                video_path = os.path.join(user_dir, f"{file_prefix}.mp4")
                if self.download_video(video_url, video_path):
                    downloaded_files['video'] = video_path
                    # 记录视频尺寸信息
                    logger.info(f"  视频尺寸: {video_data.get('width', 'N/A')}x{video_data.get('height', 'N/A')}")
                    logger.info(f"  视频URL: {video_url}")
            
            # 下载视频封面（也下载原图）
            cover_image = post.get('image', {})
            cover_image_id = cover_image.get('imageId')
            
            if cover_image_id:
                cover_path = os.path.join(user_dir, f"{file_prefix}_cover.jpg")
                # 封面也下载原图
                if self.download_image(cover_image_id, cover_path, original=True):
                    downloaded_files['cover'] = cover_path
                    logger.info(f"  封面尺寸: {cover_image.get('width', 'N/A')}x{cover_image.get('height', 'N/A')}")
        
        # 不再保存本地元数据文件，所有信息都在数据库中
        
        return downloaded_files


# ==================== Supabase 数据库管理 ====================
class SupabaseManager:
    """Supabase 数据库管理器"""
    
    def __init__(self):
        """初始化 Supabase 客户端"""
        self.client: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_KEY)
    
    @staticmethod
    def build_image_url(image_id: str) -> str:
        """构建图片URL"""
        if not image_id:
            return ''
        return f"https://pic1.finkapp.cn/{image_id}"
    
    def post_exists(self, post_id: str) -> bool:
        """检查作品是否已存在"""
        try:
            result = self.client.table('finka').select('post_id').eq('post_id', post_id).execute()
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"检查作品存在性失败: {e}")
            return False
    
    def save_post(self, post_data: dict) -> Optional[str]:
        """保存作品信息到数据库
        
        处理多媒体文件：
        - 多个图片ID/URL用分号分隔
        - 视频和封面分别存储
        - file_id也用分号分隔
        """
        try:
            # 准备数据
            post = post_data.get('post', post_data)
            user = post.get('user', {})
            
            post_id = post.get('postId', post.get('id', ''))
            if not post_id:
                logger.error("作品缺少post_id")
                return None
            
            post_type = post.get('type', '')
            
            # 处理图片信息（仅当是ImagePost时）
            image_ids = []
            image_urls = []
            image_widths = []
            image_heights = []
            
            if post_type == 'ImagePost':
                # 优先检查imageInfoList（API返回的多图片格式）
                image_info_list = post.get('imageInfoList', [])
                if image_info_list and isinstance(image_info_list, list):
                    # 多张图片（新格式）
                    for info in image_info_list:
                        if isinstance(info, dict) and 'image' in info:
                            img = info['image']
                            image_ids.append(img.get('imageId', ''))
                            image_urls.append(self.build_image_url(img.get('imageId', '')))
                            image_widths.append(str(img.get('width', '')))
                            image_heights.append(str(img.get('height', '')))
                # 然后检查images字段（可能的旧格式）
                elif post.get('images', []):
                    images = post.get('images', [])
                    if isinstance(images, list):
                        # 多张图片（旧格式）
                        for img in images:
                            if isinstance(img, dict):
                                image_ids.append(img.get('imageId', ''))
                                image_urls.append(self.build_image_url(img.get('imageId', '')))
                                image_widths.append(str(img.get('width', '')))
                                image_heights.append(str(img.get('height', '')))
                else:
                    # 单张图片
                    image = post.get('image', {})
                    if image.get('imageId'):
                        image_ids.append(image.get('imageId', ''))
                        image_urls.append(self.build_image_url(image.get('imageId', '')))
                        image_widths.append(str(image.get('width', '')))
                        image_heights.append(str(image.get('height', '')))
            
            # 处理视频信息
            video_ids = []
            video_urls = []
            video_widths = []
            video_heights = []
            
            if post_type == 'VideoPost':
                # 检查多视频
                videos = post.get('videos', [])
                if videos and isinstance(videos, list):
                    # 多个视频
                    for vid in videos:
                        if isinstance(vid, dict):
                            video_ids.append(vid.get('videoId', ''))
                            video_urls.append(vid.get('url', ''))
                            video_widths.append(str(vid.get('width', '')))
                            video_heights.append(str(vid.get('height', '')))
                else:
                    # 单个视频
                    video = post.get('video', {})
                    if video.get('url'):
                        video_ids.append(video.get('videoId', ''))
                        video_urls.append(video.get('url', ''))
                        video_widths.append(str(video.get('width', '')))
                        video_heights.append(str(video.get('height', '')))
            
            # 处理视频封面（VideoPost的image字段）
            cover_image_id = ''
            cover_image_url = ''
            cover_image_width = None
            cover_image_height = None
            
            if post_type == 'VideoPost':
                cover_image = post.get('image', {})
                cover_image_id = cover_image.get('imageId', '')
                cover_image_url = self.build_image_url(cover_image_id)
                cover_image_width = cover_image.get('width')
                cover_image_height = cover_image.get('height')
            
            # 构建数据库记录
            db_record = {
                'post_id': post_id,
                'post_type': post_type,
                'description': post.get('description', ''),
                'share_link': post.get('shareLink', ''),
                'share_title': post.get('shareTitle', ''),
                'like_count': post.get('likeCount', 0),
                'comment_count': post.get('commentCount', 0),
                'feed_share_count': post.get('feedShareCount', 0),
                'latitude': post.get('latitude'),
                'longitude': post.get('longitude'),
                'venue_abroad': post.get('venueAbroad', False),
                'user_id': user.get('id', user.get('userId', '')),
                'user_name': user.get('name', ''),
                'user_nickname': user.get('nickname', ''),
                'user_age': user.get('age'),
                'user_location': user.get('location', ''),
                'user_avatar_url': user.get('avatarUrl', ''),
                'user_avatar_image_id': user.get('avatarImageId', ''),
                'user_vip': user.get('vip', False),
                'user_svip': user.get('svip', False),
                'user_ssvip': user.get('ssvip', False),
                'user_annual_vip': user.get('annualVip', False),
                'user_annual_svip': user.get('annualSvip', False),
                'user_annual_ssvip': user.get('annualSsvip', False),
                # 图片信息（可能多个，用分号分隔）
                'image_id': ';'.join(image_ids),
                'image_url': ';'.join(image_urls),
                'image_width': int(image_widths[0]) if image_widths and image_widths[0].isdigit() else None,
                'image_height': int(image_heights[0]) if image_heights and image_heights[0].isdigit() else None,
                'image_variants': post.get('image', {}).get('variants', {}),  # 只保存第一张图的variants
                # 视频信息（可能多个，用分号分隔）
                'video_id': ';'.join(video_ids),
                'video_url': ';'.join(video_urls),
                'video_width': int(video_widths[0]) if video_widths and video_widths[0].isdigit() else None,
                'video_height': int(video_heights[0]) if video_heights and video_heights[0].isdigit() else None,
                # 视频封面信息（单独存储）
                'cover_image_id': cover_image_id,
                'cover_image_url': cover_image_url,
                'cover_image_width': cover_image_width,
                'cover_image_height': cover_image_height,
                'private_feed': post.get('privateFeed', False),
                'note': post.get('note', False),
                'report_data': post.get('reportData', ''),
                'recent_comments': post.get('recentComments', []),
                'create_time_millis': post.get('createTimeMillis', post.get('createTime', 0)),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'raw_data': post,
                'file_id': post_data.get('file_id', '')  # 已经是分号分隔的
            }
            
            # 插入到数据库
            result = self.client.table('finka').insert(db_record).execute()
            
            if result.data:
                logger.info(f"✅ 作品 {post_id} 保存成功")
                return post_id
            else:
                logger.error(f"❌ 作品 {post_id} 保存失败")
                return None
                
        except Exception as e:
            logger.error(f"保存作品到数据库失败: {e}")
            return None
    
    def update_file_id(self, post_id: str, file_id: str) -> bool:
        """更新作品的 Telegram file_id"""
        try:
            result = self.client.table('finka').update({
                'file_id': file_id,
                'updated_at': datetime.now().isoformat()
            }).eq('post_id', post_id).execute()
            
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"更新 file_id 失败: {e}")
            return False


# ==================== Telegram 上传器 ====================
class TelegramUploader:
    """Telegram 媒体上传器"""
    
    def __init__(self):
        """初始化 Telegram 客户端"""
        self.app = None
        self.channel_id = Config.TELEGRAM_CHANNEL_ID
    
    async def start(self):
        """启动 Telegram 客户端"""
        self.app = TelegramClient(
            Config.TELEGRAM_SESSION_NAME,
            api_id=int(Config.TELEGRAM_API_ID),
            api_hash=Config.TELEGRAM_API_HASH,
            bot_token=Config.TELEGRAM_BOT_TOKEN
        )
        await self.app.start()
        logger.info("✅ Telegram Bot 客户端启动成功")
    
    async def stop(self):
        """停止 Telegram 客户端"""
        if self.app:
            await self.app.stop()
            logger.info("Telegram 客户端已停止")
    
    async def upload_post(self, post: dict, media_files: Dict[str, Union[str, List[str]]]) -> Optional[str]:
        """上传作品到 Telegram 并返回 file_id
        
        Returns:
            str: 如果有多个文件，返回用分号分隔的file_id列表
        """
        try:
            post_type = post.get('type', '')
            description = post.get('description', '')
            user_name = post.get('user', {}).get('name', '未知')
            
            # 构建消息文本
            caption = f"👤 作者: {user_name}\n"
            if description:
                caption += f"📝 {description}\n"
            caption += f"\n❤️ {post.get('likeCount', 0)} | 💬 {post.get('commentCount', 0)}"
            
            file_ids = []
            
            # 处理图片作品
            if post_type == 'ImagePost':
                # 检查是否有多张图片
                image_files = []
                if 'images' in media_files and isinstance(media_files['images'], list):
                    # 多张图片
                    image_files = media_files['images']
                elif 'image' in media_files:
                    # 单张图片
                    image_files = [media_files['image']]
                
                if len(image_files) > 1:
                    # 使用 media group 上传多张图片
                    from pyrogram.types import InputMediaPhoto
                    media = []
                    for i, img_path in enumerate(image_files[:10]):  # Telegram限制最多10个媒体
                        if i == 0:
                            media.append(InputMediaPhoto(media=img_path, caption=caption))
                        else:
                            media.append(InputMediaPhoto(media=img_path))
                    
                    messages = await self.app.send_media_group(
                        chat_id=self.channel_id,
                        media=media
                    )
                    
                    # 收集所有file_id
                    for msg in messages:
                        if msg.photo:
                            file_ids.append(msg.photo.file_id)
                    
                elif len(image_files) == 1:
                    # 单张图片
                    message = await self.app.send_photo(
                        chat_id=self.channel_id,
                        photo=image_files[0],
                        caption=caption
                    )
                    if message.photo:
                        file_ids.append(message.photo.file_id)
            
            # 处理视频作品
            elif post_type == 'VideoPost':
                # 检查是否有多个视频
                video_files = []
                if 'videos' in media_files and isinstance(media_files['videos'], list):
                    # 多个视频
                    video_files = media_files['videos']
                elif 'video' in media_files:
                    # 单个视频
                    video_files = [media_files['video']]
                
                if len(video_files) > 1:
                    # 使用 media group 上传多个视频
                    from pyrogram.types import InputMediaVideo
                    media = []
                    for i, vid_path in enumerate(video_files[:10]):  # Telegram限制
                        if i == 0:
                            media.append(InputMediaVideo(
                                media=vid_path,
                                caption=caption,
                                thumb=media_files.get('cover')  # 第一个视频使用封面
                            ))
                        else:
                            media.append(InputMediaVideo(media=vid_path))
                    
                    messages = await self.app.send_media_group(
                        chat_id=self.channel_id,
                        media=media
                    )
                    
                    # 收集所有file_id
                    for msg in messages:
                        if msg.video:
                            file_ids.append(msg.video.file_id)
                
                elif len(video_files) == 1:
                    # 单个视频
                    message = await self.app.send_video(
                        chat_id=self.channel_id,
                        video=video_files[0],
                        caption=caption,
                        thumb=media_files.get('cover')
                    )
                    if message.video:
                        file_ids.append(message.video.file_id)
            
            # 返回用分号分隔的file_id
            if file_ids:
                result = ";".join(file_ids)
                logger.info(f"✅ 作品上传成功，{len(file_ids)}个文件，file_ids: {result[:100]}...")
                return result
            else:
                logger.error("❌ 上传失败，未获取到 file_id")
                return None
                
        except Exception as e:
            logger.error(f"上传作品到 Telegram 失败: {e}")
            return None


# ==================== 客户端 ====================
class FinkaClient:
    """Finka 客户端 - 整合 API、下载、数据库和 Telegram 功能"""
    
    def __init__(self, auth_token: str = None, download_dir: str = None):
        """初始化 Finka 客户端"""
        self.api = FinkaAPI(auth_token)
        self.downloader = FinkaDownloader(download_dir)
        self.db = SupabaseManager()
        self.telegram = TelegramUploader()
    
    def get_all_following_users(self) -> List[dict]:
        """获取所有关注用户"""
        logger.info("开始获取关注用户列表...")
        all_users = []
        last_id = ""
        page = 1
        
        while True:
            logger.info(f"获取第 {page} 页...")
            result = self.api.get_following_list(last_id=last_id)
            
            if result:
                users = result["users"]
                all_users.extend(users)
                logger.info(f"第 {page} 页获取到 {len(users)} 个用户")
                
                if not result["hasMore"] or result["hasMore"] == 0:
                    break
                
                last_id = result["lastId"]
                page += 1
                time.sleep(Config.REQUEST_DELAY)
            else:
                logger.error("获取关注列表失败")
                break
        
        logger.info(f"共获取 {len(all_users)} 个关注用户")
        return all_users
    
    def get_all_user_posts(self, user_id: str, user_name: str, max_pages: int = None) -> List[dict]:
        """获取用户的所有作品"""
        max_pages = max_pages or Config.MAX_PAGES_PER_USER
        logger.info(f"开始获取 {user_name} (ID: {user_id}) 的作品...")
        all_posts = []
        seen_post_ids = set()
        cursor = ""
        page = 1
        consecutive_empty_pages = 0
        
        while page <= max_pages:
            logger.info(f"  获取第 {page} 页...")
            result = self.api.get_user_posts(user_id, cursor=cursor)
            
            if result and result.get("success") and result.get("data"):
                data = result["data"]
                posts = data.get("list", [])
                next_cursor = data.get("nextCursorId", "")
                
                # 去重
                new_posts = []
                for post in posts:
                    post_id = post.get('postId', post.get('id', ''))
                    if post_id and post_id not in seen_post_ids:
                        seen_post_ids.add(post_id)
                        # 添加作者信息到作品数据中
                        if 'user' not in post:
                            post['user'] = {
                                'id': user_id,
                                'name': user_name
                            }
                        new_posts.append(post)
                
                if not new_posts:
                    consecutive_empty_pages += 1
                    if consecutive_empty_pages >= Config.CONSECUTIVE_EMPTY_PAGES_LIMIT:
                        logger.info(f"  连续{Config.CONSECUTIVE_EMPTY_PAGES_LIMIT}页无新作品，停止获取")
                        break
                else:
                    consecutive_empty_pages = 0
                    all_posts.extend(new_posts)
                    logger.info(f"  第 {page} 页获取到 {len(new_posts)} 个新作品")
                
                # 检查是否有下一页
                if not next_cursor or next_cursor == cursor:
                    logger.info("  已到达最后一页")
                    break
                
                cursor = next_cursor
                page += 1
                time.sleep(Config.PAGE_REQUEST_DELAY)
            else:
                logger.error(f"  获取第 {page} 页失败")
                break
        
        logger.info(f"共获取 {user_name} 的 {len(all_posts)} 个作品")
        return all_posts
    
    def download_user_posts(self, user_id: str, user_name: str, posts: List[dict]) -> Dict[str, int]:
        """下载用户的所有作品"""
        user_dir = self.downloader._get_user_dir(user_name, user_id)
        
        stats = {
            'total': len(posts),
            'image_success': 0,
            'video_success': 0,
            'failed': 0
        }
        
        logger.info(f"开始下载 {user_name} 的 {len(posts)} 个作品...")
        
        for i, post in enumerate(posts, 1):
            post_type = post.get('type', '')
            post_id = post.get('postId', post.get('id', ''))
            
            logger.info(f"[{i}/{len(posts)}] 下载作品 {post_id} ({post_type})...")
            
            try:
                downloaded_files = self.downloader.download_post_content(post, user_dir, user_id)
                
                if post_type == 'ImagePost' and 'image' in downloaded_files:
                    stats['image_success'] += 1
                elif post_type == 'VideoPost' and 'video' in downloaded_files:
                    stats['video_success'] += 1
                else:
                    stats['failed'] += 1
                    
            except Exception as e:
                logger.error(f"下载作品 {post_id} 失败: {e}")
                stats['failed'] += 1
            
            # 避免下载过快
            if i < len(posts):
                time.sleep(Config.REQUEST_DELAY)
        
        logger.info(f"下载完成统计: 图片 {stats['image_success']}, 视频 {stats['video_success']}, 失败 {stats['failed']}")
        return stats
    
    async def process_single_post(self, post: dict, user_name: str) -> bool:
        """处理单个作品的完整流程"""
        try:
            post_id = post.get('postId', post.get('id', ''))
            post_type = post.get('type', '')
            
            # 1. 检查作品是否已存在
            if self.db.post_exists(post_id):
                logger.info(f"作品 {post_id} 已存在，跳过")
                return True
            
            # 2. 下载媒体文件
            author_id = post.get('user', {}).get('id', '')
            user_dir = self.downloader._get_user_dir(user_name, author_id)
            downloaded_files = self.downloader.download_post_content(post, user_dir, author_id)
            
            if not downloaded_files:
                logger.error(f"作品 {post_id} 下载失败")
                return False
            
            # 3. 上传到 Telegram
            file_id = await self.telegram.upload_post(post, downloaded_files)
            
            # 4. 保存到数据库
            post_data = {
                'post': post,
                'file_id': file_id or ''
            }
            
            saved_post_id = self.db.save_post(post_data)
            
            if saved_post_id:
                logger.info(f"✅ 作品 {post_id} 处理完成")
                return True
            else:
                logger.error(f"❌ 作品 {post_id} 保存失败")
                return False
                
        except Exception as e:
            logger.error(f"处理作品失败: {e}")
            return False
    
    async def process_user_posts(self, user_id: str, user_name: str, max_pages: int = None) -> Dict[str, int]:
        """处理用户的所有作品"""
        # 获取用户所有作品
        posts = self.get_all_user_posts(user_id, user_name, max_pages)
        
        stats = {
            'total': len(posts),
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        logger.info(f"开始处理 {user_name} 的 {len(posts)} 个作品...")
        
        for i, post in enumerate(posts, 1):
            post_id = post.get('postId', post.get('id', ''))
            logger.info(f"[{i}/{len(posts)}] 处理作品 {post_id}...")
            
            # 检查是否已存在
            if self.db.post_exists(post_id):
                logger.info(f"作品 {post_id} 已存在，跳过")
                stats['skipped'] += 1
                continue
            
            # 处理作品
            success = await self.process_single_post(post, user_name)
            
            if success:
                stats['success'] += 1
            else:
                stats['failed'] += 1
            
            # 避免处理过快
            if i < len(posts):
                await asyncio.sleep(Config.REQUEST_DELAY)
        
        logger.info(f"处理完成: 成功 {stats['success']}, 失败 {stats['failed']}, 跳过 {stats['skipped']}")
        return stats
    
    def find_author_with_few_posts(self, max_posts: int = 100) -> Optional[dict]:
        """查找作品数量少于指定数量的作者
        
        Args:
            max_posts: 最大作品数量限制
            
        Returns:
            包含作者信息和作品数量的字典，如果没找到返回None
        """
        logger.info(f"开始查找作品数量少于 {max_posts} 的作者...")
        
        # 获取关注列表
        result = self.api.get_following_list(count=30)
        
        if not result or not result.get("users"):
            logger.error("无法获取关注用户列表")
            return None
            
        users = result["users"]
        logger.info(f"获取到 {len(users)} 个用户，开始逐个检查...")
        
        for idx, user in enumerate(users, 1):
            user_id = user.get('id', user.get('userId', ''))
            user_name = user.get('name', '未知')
            
            logger.info(f"[{idx}/{len(users)}] 检查用户: {user_name} (ID: {user_id})")
            
            # 快速估算作品数量
            posts_count = self._estimate_user_posts_count(user_id, user_name, max_posts)
            
            if posts_count is not None and posts_count < max_posts:
                logger.info(f"✅ 找到符合条件的作者: {user_name}")
                logger.info(f"   作品数量: {posts_count}")
                return {
                    'id': user_id,
                    'name': user_name,
                    'posts_count': posts_count
                }
            
            time.sleep(0.5)  # 避免请求过快
            
        logger.warning("未找到符合条件的作者")
        return None
    
    def _estimate_user_posts_count(self, user_id: str, user_name: str, max_check: int = 100) -> Optional[int]:
        """快速估算用户作品数量
        
        Args:
            user_id: 用户ID
            user_name: 用户名
            max_check: 最多检查的作品数
            
        Returns:
            作品数量，如果超过max_check返回None
        """
        total_posts = 0
        cursor = ""
        page = 1
        max_pages = 5  # 最多检查5页来快速估算
        
        while page <= max_pages:
            result = self.api.get_user_posts(user_id, count=21, cursor=cursor)
            
            if result and result.get("success") and result.get("data"):
                data = result["data"]
                posts = data.get("list", [])
                total_posts += len(posts)
                
                # 如果这一页不满，说明没有更多了
                if len(posts) < 21:
                    return total_posts
                
                # 如果已经超过限制，直接返回
                if total_posts >= max_check:
                    return None
                
                cursor = data.get("nextCursorId", "")
                if not cursor:
                    return total_posts
                    
                page += 1
                time.sleep(0.3)
            else:
                break
                
        # 如果检查了max_pages页还有更多，返回None表示太多
        return None if total_posts >= max_check else total_posts
    
    async def download_small_author(self, max_posts: int = 100) -> Optional[dict]:
        """查找并下载作品数量少的作者的全部作品
        
        Args:
            max_posts: 最大作品数量限制
            
        Returns:
            下载统计信息
        """
        # 查找符合条件的作者
        author = self.find_author_with_few_posts(max_posts)
        
        if not author:
            logger.error("未找到符合条件的作者")
            return None
        
        user_id = author['id']
        user_name = author['name']
        posts_count = author['posts_count']
        
        logger.info(f"\n{'='*60}")
        logger.info(f"准备下载作者: {user_name} (ID: {user_id})")
        logger.info(f"作品数量: {posts_count}")
        logger.info(f"{'='*60}")
        
        # 启动 Telegram 客户端
        await self.telegram.start()
        
        try:
            # 获取该作者的所有作品
            posts = self.get_all_user_posts(user_id, user_name)
            
            # 统计信息
            stats = {
                'author_id': user_id,
                'author_name': user_name,
                'total_posts': len(posts),
                'image_posts': 0,
                'video_posts': 0,
                'success': 0,
                'failed': 0,
                'skipped': 0
            }
            
            # 统计作品类型
            for post in posts:
                post_type = post.get('type', '')
                if post_type == 'ImagePost':
                    stats['image_posts'] += 1
                elif post_type == 'VideoPost':
                    stats['video_posts'] += 1
            
            logger.info(f"\n📊 作品统计:")
            logger.info(f"  总作品数: {stats['total_posts']}")
            logger.info(f"  图片作品: {stats['image_posts']}")
            logger.info(f"  视频作品: {stats['video_posts']}")
            
            # 创建用户目录
            user_dir = self.downloader._get_user_dir(user_name, user_id)
            logger.info(f"\n📁 下载目录: {user_dir}")
            
            # 处理每个作品
            for idx, post in enumerate(posts, 1):
                post_id = post.get('postId', post.get('id', ''))
                post_type = post.get('type', '')
                
                logger.info(f"\n[{idx}/{len(posts)}] 处理作品 {post_id} ({post_type})")
                
                # 检查是否已存在
                if self.db.post_exists(post_id):
                    logger.info(f"  作品已存在，跳过")
                    stats['skipped'] += 1
                    continue
                
                # 处理作品
                success = await self.process_single_post(post, user_name)
                
                if success:
                    stats['success'] += 1
                else:
                    stats['failed'] += 1
                
                # 避免处理过快
                if idx < len(posts):
                    await asyncio.sleep(Config.REQUEST_DELAY)
            
            # 输出统计
            logger.info(f"\n{'='*60}")
            logger.info(f"✅ 下载完成 - {user_name} 的统计信息")
            logger.info(f"{'='*60}")
            logger.info(f"作者: {user_name} (ID: {user_id})")
            logger.info(f"总作品数: {stats['total_posts']}")
            logger.info(f"  - 图片作品: {stats['image_posts']}")
            logger.info(f"  - 视频作品: {stats['video_posts']}")
            logger.info(f"处理成功: {stats['success']}")
            logger.info(f"处理失败: {stats['failed']}")
            logger.info(f"跳过已存在: {stats['skipped']}")
            logger.info(f"{'='*60}")
            
            # 保存统计到文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            # 清理文件名中的特殊字符
            safe_name = "".join(c for c in user_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_')
            stats_file = f"download_stats_{safe_name}_{timestamp}.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 统计已保存到: {stats_file}")
            
            return stats
            
        finally:
            # 停止 Telegram 客户端
            await self.telegram.stop()
    
    async def process_all_following_users_async(self, download_content: bool = True):
        """异步处理所有关注用户"""
        # 启动 Telegram 客户端
        await self.telegram.start()
        
        try:
            # 获取所有关注用户
            users = self.get_all_following_users()
            if not users:
                logger.error("未获取到任何关注用户")
                return
            
            # 统计信息
            total_stats = {
                'users': len(users),
                'total_posts': 0,
                'success': 0,
                'failed': 0,
                'skipped': 0
            }
            
            # 处理每个用户
            for idx, user in enumerate(users, 1):
                user_id = user.get('id', user.get('userId', ''))
                user_name = user.get('name', '未知')
                
                logger.info(f"\n{'='*60}")
                logger.info(f"[{idx}/{len(users)}] 处理用户: {user_name} (ID: {user_id})")
                logger.info(f"{'='*60}")
                
                # 处理用户的所有作品
                stats = await self.process_user_posts(user_id, user_name)
                
                total_stats['total_posts'] += stats['total']
                total_stats['success'] += stats['success']
                total_stats['failed'] += stats['failed']
                total_stats['skipped'] += stats['skipped']
                
                # 避免处理过快
                if idx < len(users):
                    await asyncio.sleep(Config.USER_PROCESS_DELAY)
            
            # 输出总统计
            logger.info(f"\n{'='*60}")
            logger.info("处理完成 - 总体统计")
            logger.info(f"{'='*60}")
            logger.info(f"用户总数: {total_stats['users']}")
            logger.info(f"作品总数: {total_stats['total_posts']}")
            logger.info(f"处理成功: {total_stats['success']}")
            logger.info(f"处理失败: {total_stats['failed']}")
            logger.info(f"跳过已存在: {total_stats['skipped']}")
            
        finally:
            # 停止 Telegram 客户端
            await self.telegram.stop()
    
    def process_all_following_users(self, download_content: bool = True):
        """处理所有关注用户 - 获取作品并下载"""
        # 获取所有关注用户
        users = self.get_all_following_users()
        if not users:
            logger.error("未获取到任何关注用户")
            return
        
        # 统计信息
        total_stats = {
            'users': len(users),
            'total_posts': 0,
            'total_images': 0,
            'total_videos': 0,
            'download_success': 0,
            'download_failed': 0
        }
        
        # 处理每个用户
        for idx, user in enumerate(users, 1):
            user_id = user.get('id', user.get('userId', ''))
            user_name = user.get('name', '未知')
            
            logger.info(f"\n{'='*60}")
            logger.info(f"[{idx}/{len(users)}] 处理用户: {user_name} (ID: {user_id})")
            logger.info(f"{'='*60}")
            
            # 获取用户所有作品
            posts = self.get_all_user_posts(user_id, user_name)
            total_stats['total_posts'] += len(posts)
            
            # 统计作品类型
            image_posts = [p for p in posts if p.get('type') == 'ImagePost']
            video_posts = [p for p in posts if p.get('type') == 'VideoPost']
            total_stats['total_images'] += len(image_posts)
            total_stats['total_videos'] += len(video_posts)
            
            logger.info(f"作品统计: 总计 {len(posts)}, 图片 {len(image_posts)}, 视频 {len(video_posts)}")
            
            # 下载内容
            if download_content and posts:
                stats = self.download_user_posts(user_id, user_name, posts)
                total_stats['download_success'] += stats['image_success'] + stats['video_success']
                total_stats['download_failed'] += stats['failed']
            
            # 避免处理过快
            if idx < len(users):
                time.sleep(Config.USER_PROCESS_DELAY)
        
        # 输出总统计
        logger.info(f"\n{'='*60}")
        logger.info("处理完成 - 总体统计")
        logger.info(f"{'='*60}")
        logger.info(f"用户总数: {total_stats['users']}")
        logger.info(f"作品总数: {total_stats['total_posts']}")
        logger.info(f"  - 图片: {total_stats['total_images']}")
        logger.info(f"  - 视频: {total_stats['total_videos']}")
        if download_content:
            logger.info(f"下载成功: {total_stats['download_success']}")
            logger.info(f"下载失败: {total_stats['download_failed']}")


# ==================== 主函数 ====================
def print_config():
    """打印当前配置"""
    logger.info("当前配置:")
    logger.info(f"  认证Token: {Config.AUTH_TOKEN[:20]}...")
    logger.info(f"  下载目录: {Config.DOWNLOAD_DIR}")
    logger.info(f"  APP版本: {Config.APP_VERSION}")
    logger.info(f"  每用户最大页数: {Config.MAX_PAGES_PER_USER}")
    logger.info(f"  请求超时: {Config.REQUEST_TIMEOUT}秒")
    logger.info(f"  显示下载进度: {Config.SHOW_DOWNLOAD_PROGRESS}")
    logger.info("")


async def test_single_post():
    """测试单个作品的处理流程"""
    client = FinkaClient()
    
    # 启动 Telegram 客户端
    await client.telegram.start()
    
    try:
        # 获取第一个用户的第一个作品进行测试
        users = client.get_all_following_users()
        if users:
            user = users[0]
            user_id = user.get('id', user.get('userId', ''))
            user_name = user.get('name', '未知')
            
            logger.info(f"测试用户: {user_name} (ID: {user_id})")
            
            # 获取该用户的作品
            posts = client.get_all_user_posts(user_id, user_name, max_pages=1)
            
            if posts:
                # 找到第一个未处理的作品
                processed = False
                for post in posts:
                    post_id = post.get('postId', post.get('id', ''))
                    if not client.db.post_exists(post_id):
                        logger.info(f"测试作品: {post_id}")
                        # 处理单个作品
                        success = await client.process_single_post(post, user_name)
                        
                        if success:
                            logger.info("✅ 测试成功！")
                        else:
                            logger.error("❌ 测试失败！")
                        processed = True
                        break
                
                if not processed:
                    logger.info("该用户的所有作品已处理，尝试下一个用户...")
                    # 尝试下一个用户
                    if len(users) > 1:
                        user = users[1]
                        user_id = user.get('id', user.get('userId', ''))
                        user_name = user.get('name', '未知')
                        posts = client.get_all_user_posts(user_id, user_name, max_pages=1)
                        if posts:
                            post = posts[0]
                            logger.info(f"测试作品: {post.get('postId', '')}")
                            success = await client.process_single_post(post, user_name)
                            if success:
                                logger.info("✅ 测试成功！")
                            else:
                                logger.error("❌ 测试失败！")
            else:
                logger.error("该用户没有作品")
        else:
            logger.error("没有关注用户")
    finally:
        await client.telegram.stop()


async def main_async():
    """异步主函数"""
    # 打印配置
    print_config()
    
    # 创建客户端
    client = FinkaClient()
    
    # 处理所有关注用户
    await client.process_all_following_users_async(download_content=True)


async def download_small_author_main():
    """下载作品数量少的作者"""
    # 打印配置
    print_config()
    
    # 创建客户端
    client = FinkaClient()
    
    # 下载作品数量少于100的作者
    stats = await client.download_small_author(max_posts=100)
    
    if stats:
        logger.info("\n✅ 下载任务完成！")
    else:
        logger.error("\n❌ 下载任务失败或未找到合适的作者")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            # 测试模式：只处理一个作品
            logger.info("运行测试模式...")
            asyncio.run(test_single_post())
        elif sys.argv[1] == 'small':
            # 下载小作者模式：查找并下载作品少于100的作者
            logger.info("运行小作者下载模式...")
            asyncio.run(download_small_author_main())
        else:
            logger.error(f"未知参数: {sys.argv[1]}")
            logger.info("用法:")
            logger.info("  python finka.py          # 处理所有关注用户")
            logger.info("  python finka.py test     # 测试模式")
            logger.info("  python finka.py small    # 下载作品少于100的作者")
    else:
        # 正常模式：处理所有用户的所有作品
        asyncio.run(main_async())
    


if __name__ == "__main__":
    # 使用说明：
    # 1. 测试模式（只处理一个作品）：
    #    python finka.py test
    #
    # 2. 小作者模式（查找并下载作品少于100的作者）：
    #    python finka.py small
    #
    # 3. 正常模式（处理所有用户的所有作品）：
    #    python finka.py
    #
    # 配置说明：
    # 1. 可以直接修改 Config 类中的参数
    # 2. 也可以通过环境变量覆盖，例如：
    #    export FINKA_AUTH_TOKEN="your_token_here"
    #    export TELEGRAM_API_ID="your_api_id"
    #    export TELEGRAM_API_HASH="your_api_hash"
    #    export TELEGRAM_CHANNEL_ID="your_channel_id"
    #    python finka.py
    
    main()