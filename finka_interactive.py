#!/usr/bin/env python3
"""
Finka 交互式客户端
整合获取关注用户和作品的完整流程，包含API验证功能
"""

import time
import json
import os
import hashlib
import requests
from datetime import datetime
from urllib.parse import urlencode


class FinkaAPI:
    def __init__(self, auth_token):
        """
        初始化 Finka API 客户端
        
        Args:
            auth_token: 格式为 "access_token|user_id|refresh_token"
        """
        self.auth_token = auth_token
        parts = auth_token.split('|')
        if len(parts) >= 2:
            self.access_token = parts[0]
            self.user_id = parts[1]
            self.refresh_token = parts[2] if len(parts) > 2 else ""
        else:
            raise ValueError("Invalid auth token format")
        
        self.base_url = "https://api.finka.cn"
        self.session = requests.Session()
        self._setup_headers()
    
    def _setup_headers(self):
        """设置请求头"""
        self.session.headers.update({
            'X-App-Auth': self.auth_token,
            'X-App-Version': '8.12.0',
            'X-App-Device': 'Finka0a%2F8.12.0+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+250725002%2FOfficial%3B%29',
            'X-App-GUID': '484837FB-D8BD-4312-A893-8F04B4EE994A',
            'X-App-User': self.user_id,
            'Android-Store': 'Official',
            'Accept-Language': 'en-US',
            'User-Agent': 'Finka/8.12.0 (Android 12; Google Pixel 3 XL)',
            'Accept-Encoding': 'gzip',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })
    
    def _generate_digest(self, method, path, params, body=""):
        """生成请求的 MD5 摘要"""
        # 构建待签名字符串
        query_string = ""
        if params:
            sorted_params = sorted(params.items())
            query_string = urlencode(sorted_params)
        
        # 格式：METHOD + PATH + QUERY + BODY
        sign_string = f"{method.upper()}{path}"
        if query_string:
            sign_string += f"?{query_string}"
        if body:
            sign_string += body
        
        # 计算 MD5
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def request(self, method, path, params=None, data=None):
        """
        发送 API 请求
        
        Args:
            method: HTTP 方法 (GET/POST)
            path: API 路径
            params: 查询参数
            data: POST 数据
        
        Returns:
            解析后的 JSON 响应
        """
        # 添加时间戳
        if params is None:
            params = {}
        params['_t'] = str(int(time.time() * 1000))
        
        # 准备请求体
        body_string = ""
        if data:
            if isinstance(data, dict):
                body_string = urlencode(data)
            else:
                body_string = data
        
        # 生成摘要
        digest = self._generate_digest(method, path, params, body_string)
        
        # 构建完整 URL
        url = self.base_url + path
        
        # 更新请求头
        headers = {'Digest': digest}
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, headers=headers)
            else:
                response = self.session.post(url, params=params, data=body_string, headers=headers)
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"API 请求失败: {e}")
            return None
    
    def get_following_list(self, count=30, last_id=""):
        """
        获取关注列表
        
        Args:
            count: 每页数量
            last_id: 分页参数
        
        Returns:
            API 响应
        """
        data = {"count": str(count)}
        if last_id:
            data["lastId"] = last_id
        
        result = self.request("POST", "/user/match/liked/v3/active/v3", data=urlencode(data))
        
        if result and result.get("success") and result.get("data"):
            users = result["data"].get("list", [])
            has_more = result["data"].get("hasMore", 0)
            next_last_id = result["data"].get("lastId", "")
            
            return {
                "users": users,
                "hasMore": has_more,
                "lastId": next_last_id,
                "total": len(users)
            }
        
        return None


class FinkaInteractiveClient:
    def __init__(self, auth_token):
        self.api = FinkaAPI(auth_token)
        self.following_users = []
        self.posts_dir = "finka_posts"
        
        # 创建保存目录
        if not os.path.exists(self.posts_dir):
            os.makedirs(self.posts_dir)
    
    def load_or_fetch_following_users(self):
        """加载或获取关注用户列表"""
        cache_file = 'following_users_cache.json'
        
        # 检查缓存文件
        if os.path.exists(cache_file):
            # 检查缓存时间
            file_time = os.path.getmtime(cache_file)
            current_time = time.time()
            # 如果缓存超过1小时，提示更新
            if current_time - file_time > 3600:
                print("\n缓存的关注列表已超过1小时。")
                choice = input("是否重新获取最新数据？(y/n): ").strip().lower()
                if choice == 'y':
                    return self.fetch_following_users()
            
            # 加载缓存
            print("\n正在加载缓存的关注列表...")
            with open(cache_file, 'r', encoding='utf-8') as f:
                self.following_users = json.load(f)
            print(f"加载成功！共 {len(self.following_users)} 个关注用户")
            return True
        else:
            return self.fetch_following_users()
    
    def fetch_following_users(self):
        """获取所有关注用户"""
        print("\n正在获取关注列表...")
        all_users = []
        last_id = ""
        page = 1
        
        while True:
            print(f"获取第 {page} 页...")
            result = self.api.get_following_list(count=30, last_id=last_id)
            
            if result:
                users = result["users"]
                all_users.extend(users)
                print(f"第 {page} 页获取到 {len(users)} 个用户")
                
                if not result["hasMore"] or result["hasMore"] == 0:
                    break
                
                last_id = result["lastId"]
                page += 1
                time.sleep(1)
            else:
                print("获取关注列表失败")
                return False
        
        self.following_users = all_users
        
        # 保存缓存
        with open('following_users_cache.json', 'w', encoding='utf-8') as f:
            json.dump(all_users, f, ensure_ascii=False, indent=2)
        
        print(f"\n成功获取 {len(all_users)} 个关注用户")
        return True
    
    def display_users_list(self):
        """显示用户列表"""
        print("\n" + "=" * 80)
        print("关注用户列表")
        print("=" * 80)
        
        for i, user in enumerate(self.following_users, 1):
            name = user.get('name', '未知')
            user_id = user.get('id', user.get('userId', ''))
            age = user.get('age', '?')
            location = user.get('location', '未知')
            
            # 格式化输出
            print(f"{i:3d}. {name:<20} (ID: {user_id}) - {age}岁, {location}")
        
        print("=" * 80)
        print(f"总计: {len(self.following_users)} 个用户")
    
    def verify_api_pagination(self, user_id, user_name):
        """验证API分页功能，测试是否能获取完整作品"""
        print(f"\n{'='*80}")
        print(f"开始验证 {user_name} (ID: {user_id}) 的作品获取")
        print(f"{'='*80}")
        
        all_posts = []
        seen_post_ids = set()
        cursor = ""
        page = 1
        consecutive_empty_pages = 0
        max_pages = 50  # 增加最大页数以测试完整性
        
        # 统计信息
        total_images = 0
        total_videos = 0
        oldest_post_time = None
        newest_post_time = None
        
        while page <= max_pages:
            print(f"\n第 {page} 页:")
            print(f"  使用游标: {cursor[:20]}..." if cursor else "  首页请求（无游标）")
            
            params = {
                "count": "21",
                "uid": user_id
            }
            if cursor:
                params["cursor"] = cursor
            
            # 发送请求
            start_time = time.time()
            result = self.api.request("GET", "/feed/user/v3", params)
            request_time = time.time() - start_time
            print(f"  请求耗时: {request_time:.2f}秒")
            
            if result and result.get("success") and result.get("data"):
                data = result["data"]
                posts = data.get("list", [])
                next_cursor = data.get("nextCursorId", "")
                has_more = data.get("hasMore", False)
                
                # 分析本页数据
                new_posts = 0
                duplicate_posts = 0
                
                for post in posts:
                    post_id = post.get('postId', post.get('id', ''))
                    if post_id:
                        if post_id not in seen_post_ids:
                            seen_post_ids.add(post_id)
                            all_posts.append(post)
                            new_posts += 1
                            
                            # 更新统计
                            post_type = post.get('type', '')
                            if post_type == 'ImagePost':
                                total_images += 1
                            elif post_type == 'VideoPost':
                                total_videos += 1
                            
                            # 更新时间范围
                            create_time = post.get('createTime', 0)
                            if create_time:
                                if oldest_post_time is None or create_time < oldest_post_time:
                                    oldest_post_time = create_time
                                if newest_post_time is None or create_time > newest_post_time:
                                    newest_post_time = create_time
                        else:
                            duplicate_posts += 1
                
                print(f"  本页作品数: {len(posts)}")
                print(f"  新作品数: {new_posts}")
                print(f"  重复作品数: {duplicate_posts}")
                print(f"  累计作品数: {len(all_posts)}")
                print(f"  hasMore: {has_more}")
                print(f"  下一页游标: {next_cursor[:20]}..." if next_cursor else "  无下一页游标")
                
                # 检查是否应该继续
                if new_posts == 0:
                    consecutive_empty_pages += 1
                    print(f"  ⚠️  连续 {consecutive_empty_pages} 页无新作品")
                    if consecutive_empty_pages >= 3:
                        print("\n🛑 连续3页无新作品，停止获取")
                        break
                else:
                    consecutive_empty_pages = 0
                
                # 更新游标
                if next_cursor and next_cursor != cursor:
                    cursor = next_cursor
                    page += 1
                    time.sleep(0.5)  # 避免请求过快
                else:
                    print("\n✅ 游标未变化或无游标，已到达最后")
                    break
            else:
                print(f"  ❌ 请求失败")
                if result:
                    print(f"  错误信息: {result}")
                break
        
        # 输出验证结果
        print(f"\n{'='*80}")
        print("验证结果汇总")
        print(f"{'='*80}")
        print(f"总页数: {page}")
        print(f"获取作品总数: {len(all_posts)}")
        print(f"图片作品: {total_images}")
        print(f"视频作品: {total_videos}")
        
        if oldest_post_time and newest_post_time:
            oldest_date = datetime.fromtimestamp(oldest_post_time / 1000).strftime('%Y-%m-%d')
            newest_date = datetime.fromtimestamp(newest_post_time / 1000).strftime('%Y-%m-%d')
            print(f"时间范围: {oldest_date} 至 {newest_date}")
        
        # 检查作品连续性
        if len(all_posts) > 0:
            print(f"\n作品ID分布:")
            # 按时间排序
            sorted_posts = sorted(all_posts, key=lambda x: x.get('createTime', 0), reverse=True)
            
            # 显示前5个和后5个作品
            print("\n最新5个作品:")
            for i, post in enumerate(sorted_posts[:5]):
                create_time = post.get('createTime', 0)
                date_str = datetime.fromtimestamp(create_time / 1000).strftime('%Y-%m-%d %H:%M:%S') if create_time else "未知"
                desc = post.get('desc', post.get('description', ''))[:30]
                print(f"  {i+1}. {date_str} - {desc}...")
            
            if len(sorted_posts) > 5:
                print("\n最早5个作品:")
                for i, post in enumerate(sorted_posts[-5:]):
                    create_time = post.get('createTime', 0)
                    date_str = datetime.fromtimestamp(create_time / 1000).strftime('%Y-%m-%d %H:%M:%S') if create_time else "未知"
                    desc = post.get('desc', post.get('description', ''))[:30]
                    print(f"  {i+1}. {date_str} - {desc}...")
        
        return all_posts
    
    def get_user_posts_with_stats(self, user_index):
        """获取指定用户的作品并统计"""
        if user_index < 1 or user_index > len(self.following_users):
            print("无效的用户序号")
            return
        
        user = self.following_users[user_index - 1]
        user_id = user.get('id', user.get('userId', ''))
        user_name = user.get('name', '未知')
        
        print(f"\n正在获取 {user_name} (ID: {user_id}) 的作品...")
        
        # 获取所有作品
        all_posts = []
        seen_post_ids = set()  # 用于去重
        next_cursor = ""
        prev_cursor = ""  # 记录上一个游标
        page = 1
        max_pages = 20  # 最多获取20页
        
        while page <= max_pages:
            print(f"  获取第 {page} 页...")
            params = {
                "count": "21",  # API限制，使用标准值
                "uid": user_id
            }
            if next_cursor:
                params["cursor"] = next_cursor  # 使用正确的参数名
            
            result = self.api.request("GET", "/feed/user/v3", params)
            
            if result and result.get("success") and result.get("data"):
                data = result["data"]
                posts = data.get("list", [])
                
                if not posts:
                    print("  没有更多作品")
                    break
                
                # 检查是否有新的作品
                new_posts = []
                for post in posts:
                    post_id = post.get('postId', post.get('id', ''))
                    if post_id and post_id not in seen_post_ids:
                        seen_post_ids.add(post_id)
                        new_posts.append(post)
                
                if not new_posts:
                    print("  没有新作品（可能已到最后）")
                    break
                
                all_posts.extend(new_posts)
                print(f"  第 {page} 页获取到 {len(new_posts)} 个新作品")
                
                # 获取下一页游标
                prev_cursor = next_cursor
                next_cursor = data.get("nextCursorId", "")
                
                # 如果游标没有变化，说明已经到最后
                if not next_cursor or next_cursor == prev_cursor:
                    print("  已到达最后一页")
                    break
                
                page += 1
                time.sleep(0.5)
            else:
                print("  获取失败")
                break
        
        if not all_posts:
            print("未获取到任何作品")
            return
        
        # 统计作品信息
        print(f"\n{user_name} 的作品统计:")
        print("-" * 60)
        
        # 检查API限制
        if len(all_posts) >= 20 and next_cursor == prev_cursor:
            print("⚠️  注意：API限制，只能获取最近的作品")
            print("    如需获取更多作品，请使用官方应用")
            print("")
        
        # 按类型分类
        image_posts = []
        video_posts = []
        other_posts = []
        
        total_likes = 0
        total_comments = 0
        total_shares = 0
        
        for post in all_posts:
            post_type = post.get('type', '')
            total_likes += post.get('likeCount', 0)
            total_comments += post.get('commentCount', 0)
            total_shares += post.get('feedShareCount', 0)
            
            if post_type == 'ImagePost':
                image_posts.append(post)
            elif post_type == 'VideoPost':
                video_posts.append(post)
            else:
                other_posts.append(post)
        
        print(f"总作品数: {len(all_posts)}")
        print(f"\n作品分类:")
        print(f"  📷 图片作品: {len(image_posts)} 个")
        print(f"  🎬 视频作品: {len(video_posts)} 个")
        if other_posts:
            print(f"  📝 其他类型: {len(other_posts)} 个")
        
        # 统计图片总数
        total_images = 0
        for post in image_posts:
            images = post.get('imageInfoList', post.get('images', []))
            total_images += len(images)
        
        print(f"\n详细统计:")
        print(f"  图片总数: {total_images} 张")
        print(f"  平均每个图片作品: {total_images/len(image_posts):.1f} 张" if image_posts else "")
        
        print(f"\n互动数据:")
        print(f"  ❤️  总点赞: {total_likes:,}")
        print(f"  💬 总评论: {total_comments:,}")
        print(f"  🔄 总分享: {total_shares:,}")
        print(f"  平均每作品点赞: {total_likes/len(all_posts):.1f}")
        
        # 显示最热门的作品
        print(f"\n🔥 最热门的5个作品:")
        hot_posts = sorted(all_posts, key=lambda x: x.get('likeCount', 0), reverse=True)[:5]
        for i, post in enumerate(hot_posts, 1):
            post_type = "📷" if post.get('type') == 'ImagePost' else "🎬"
            content = post.get('desc', post.get('description', ''))[:30]
            likes = post.get('likeCount', 0)
            print(f"  {i}. {post_type} {content}... - {likes} 赞")
        
        # 询问是否保存详细数据
        print("\n" + "-" * 60)
        save_choice = input("\n是否保存详细作品数据？(y/n/j[JSON原始数据]/v[验证分页]): ").strip().lower()
        if save_choice == 'y':
            self.save_user_posts(user_id, user_name, all_posts)
        elif save_choice == 'j':
            # 保存原始JSON响应
            import json
            json_file = f"raw_response_{user_name}_{user_id}.json"
            raw_data = {
                "user_info": user,
                "posts": all_posts,
                "total_count": len(all_posts),
                "fetch_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(raw_data, f, ensure_ascii=False, indent=2)
            print(f"\n✅ 原始数据已保存到: {json_file}")
        elif save_choice == 'v':
            # 执行验证
            self.verify_api_pagination(user_id, user_name)
    
    def save_user_posts(self, user_id, user_name, posts):
        """保存用户作品数据"""
        # 创建用户目录
        safe_name = "".join(c for c in user_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        user_dir = os.path.join(self.posts_dir, f"{safe_name}_{user_id}")
        if not os.path.exists(user_dir):
            os.makedirs(user_dir)
        
        # 保存完整数据
        json_file = os.path.join(user_dir, f"posts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(posts, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 数据已保存到: {json_file}")
        print(f"   共 {len(posts)} 个作品")
    
    def batch_download_mode(self):
        """批量下载模式"""
        print("\n批量获取作品模式")
        print("-" * 60)
        print("请输入要获取的用户序号，多个序号用逗号分隔")
        print("例如: 1,3,5-8,10")
        
        input_str = input("\n请输入: ").strip()
        if not input_str:
            return
        
        # 解析用户输入
        selected_indices = []
        parts = input_str.split(',')
        
        for part in parts:
            part = part.strip()
            if '-' in part:
                # 处理范围
                try:
                    start, end = map(int, part.split('-'))
                    selected_indices.extend(range(start, end + 1))
                except:
                    print(f"无效的范围: {part}")
            else:
                # 单个数字
                try:
                    selected_indices.append(int(part))
                except:
                    print(f"无效的数字: {part}")
        
        # 去重并排序
        selected_indices = sorted(set(selected_indices))
        
        # 验证范围
        valid_indices = [i for i in selected_indices if 1 <= i <= len(self.following_users)]
        
        if not valid_indices:
            print("没有有效的用户序号")
            return
        
        print(f"\n将获取以下 {len(valid_indices)} 个用户的作品:")
        for idx in valid_indices:
            user = self.following_users[idx - 1]
            print(f"  {idx}. {user.get('name', '未知')}")
        
        confirm = input("\n确认开始获取？(y/n): ").strip().lower()
        if confirm != 'y':
            return
        
        # 批量获取
        for i, idx in enumerate(valid_indices, 1):
            print(f"\n[{i}/{len(valid_indices)}] ", end='')
            self.get_user_posts_with_stats(idx)
            if i < len(valid_indices):
                time.sleep(2)  # 避免请求过快
    
    def search_user(self):
        """搜索用户"""
        keyword = input("\n请输入搜索关键词: ").strip()
        if not keyword:
            return
        
        results = []
        for i, user in enumerate(self.following_users, 1):
            name = user.get('name', '')
            if keyword.lower() in name.lower():
                results.append((i, user))
        
        if results:
            print(f"\n找到 {len(results)} 个匹配的用户:")
            for idx, user in results:
                name = user.get('name', '未知')
                user_id = user.get('id', '')
                print(f"{idx:3d}. {name} (ID: {user_id})")
        else:
            print("没有找到匹配的用户")
    
    def test_api_completeness(self):
        """测试API完整性 - 验证多个用户的作品获取"""
        print("\n" + "=" * 80)
        print("API 完整性测试")
        print("=" * 80)
        print("将随机选择3个用户进行深度验证...")
        
        # 随机选择3个用户
        import random
        test_users = random.sample(self.following_users, min(3, len(self.following_users)))
        
        for i, user in enumerate(test_users, 1):
            user_id = user.get('id', user.get('userId', ''))
            user_name = user.get('name', '未知')
            print(f"\n[{i}/3] 测试用户: {user_name}")
            posts = self.verify_api_pagination(user_id, user_name)
            
            if i < len(test_users):
                print("\n等待5秒后继续下一个用户...")
                time.sleep(5)
        
        print("\n" + "=" * 80)
        print("API 完整性测试完成！")
        print("=" * 80)
    
    def get_user_posts_interactive(self):
        """交互式获取用户作品"""
        self.display_users_list()
        print("\n请输入用户序号查看作品统计")
        print("输入 0 返回主菜单")
        
        try:
            user_num = int(input("\n请选择用户 (1-{}): ".format(len(self.following_users))).strip())
            if user_num == 0:
                return
            self.get_user_posts_with_stats(user_num)
        except ValueError:
            print("请输入有效的数字")
    
    def run(self):
        """主程序循环"""
        print("\n🎉 欢迎使用 Finka 交互式客户端")
        print("=" * 80)
        
        # 加载或获取关注用户
        if not self.load_or_fetch_following_users():
            print("无法获取关注用户列表，程序退出")
            return
        
        # 首次启动直接显示用户列表
        self.display_users_list()
        print("\n提示：直接输入用户序号查看作品，或输入 m 进入主菜单")
        
        while True:
            choice = input("\n请选择用户 (1-{}) 或操作 [m:菜单/q:退出]: ".format(len(self.following_users))).strip().lower()
            
            if choice == 'q':
                print("\n感谢使用，再见！")
                break
            elif choice == 'm':
                # 显示主菜单
                self.show_main_menu()
            else:
                # 尝试解析为数字
                try:
                    user_num = int(choice)
                    if 1 <= user_num <= len(self.following_users):
                        self.get_user_posts_with_stats(user_num)
                    else:
                        print("无效的用户序号")
                except ValueError:
                    print("无效输入，请输入数字或命令（m:菜单, q:退出）")
    
    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "=" * 80)
            print("主菜单")
            print("=" * 80)
            print("1. 显示所有关注用户")
            print("2. 获取指定用户的作品统计")
            print("3. 批量获取多个用户作品")
            print("4. 搜索用户")
            print("5. 更新关注列表")
            print("6. 测试API完整性（验证分页功能）")
            print("0. 返回快速选择模式")
            print("-" * 80)
            
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == '0':
                # 返回快速选择模式
                self.display_users_list()
                return
            elif choice == '1':
                self.display_users_list()
            elif choice == '2':
                self.get_user_posts_interactive()
            elif choice == '3':
                self.display_users_list()
                self.batch_download_mode()
            elif choice == '4':
                self.search_user()
            elif choice == '5':
                self.fetch_following_users()
                self.display_users_list()
            elif choice == '6':
                self.test_api_completeness()
            else:
                print("无效的选择，请重试")


def main():
    # 使用实际的token
    AUTH_TOKEN = "aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY"
    
    # 创建客户端并运行
    client = FinkaInteractiveClient(AUTH_TOKEN)
    client.run()


if __name__ == "__main__":
    main()