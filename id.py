# anchor_processor.py
import redis
import psycopg2
from psycopg2 import pool
import logging
import json
from datetime import datetime
import pytz

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

r = redis.Redis(host='localhost', port=6379, db=0)

pool = pool.SimpleConnectionPool(
    minconn=2,
    maxconn=30,
    host='aws-0-us-west-1.pooler.supabase.com',
    port=6543,
    dbname='postgres',
    user='postgres.wjanjmsywbydjbfrdkaz',
    password='4gyp2MJp84zxW.F',
    connect_timeout=60
)

def get_current_timezone():
    return datetime.now().astimezone().tzinfo

def update_anchor_info(message_data_list):
    conn = pool.getconn()
    try:
        with conn.cursor() as cur:
            update_query = """
            UPDATE anchors
            SET anchor_name = COALESCE(%s, anchor_name),
                stream_url = COALESCE(%s, stream_url),
                status = COALESCE(%s, status),
                title = COALESCE(%s, title),
                new_url = CASE WHEN %s IS NOT NULL THEN %s ELSE new_url END
            WHERE anchor_id = %s
            """
            for message_data in message_data_list:
                anchor_id = message_data.get('anchor_id')
                anchor_name = message_data.get('anchor_name')
                stream_url = message_data.get('stream_url')
                status = message_data.get('status')
                title = message_data.get('title')
                new_url = message_data.get('new_url')
                
                anchor_name = None if anchor_name == '' else anchor_name
                stream_url = None if stream_url == '' else stream_url
                status = None if status == '' else status
                title = None if title == '' else title
                new_url = None if new_url == '' else new_url
                
                cur.execute(update_query, (anchor_name, stream_url, status, title, new_url, new_url, anchor_id))
            
            conn.commit()
    except Exception as e:
        logger.error(f"Error updating anchor info: {str(e)}")
        conn.rollback()
    finally:
        pool.putconn(conn)

def process_anchor_messages(messages):
    try:
        message_data_list = []
        for message in messages:
            if message['type'] == 'message':
                data = message['data'].decode('utf-8')
                logger.info(f"Received anchor message: {data}")
                json_data = json.loads(data[7:])
                message_data_list.append(json_data)
        
        if message_data_list:
            update_anchor_info(message_data_list)
    except Exception as e:
        logger.error(f"Error processing anchor messages: {str(e)}")
        logger.error(f"Invalid anchor message data: {messages}")

def main():
    pubsub = r.pubsub()
    pubsub.subscribe('anchor')
    logger.info("Starting anchor message processor...")
    
    try:
        for message in pubsub.listen():
            process_anchor_messages([message])
    except Exception as e:
        logger.error(f"Error in anchor processor: {str(e)}")

if __name__ == '__main__':
    main()