# status_id_processor.py

import redis
import logging
from datetime import datetime
import uuid
from dateutil import parser, tz
import os
import requests

# ==============================
# 配置区域：请根据实际情况修改
# ==============================
SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"

TSID_TABLE = "anchor_tsid"
MP4ID_TABLE = "anchor_mp4id"

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 连接 Redis
r = redis.Redis(host='localhost', port=6379, db=0)

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json"
}

def is_valid_uuid(val):
    try:
        uuid.UUID(str(val))
        return True
    except ValueError:
        return False

def parse_record_time(record_time_str):
    logger.debug(f"Parsing record_time: {record_time_str}")
    try:
        # 尝试作为 UNIX 时间戳解析
        timestamp = int(record_time_str)
        return datetime.fromtimestamp(timestamp, tz=tz.tzlocal())
    except ValueError:
        # 尝试作为 ISO8601 字符串解析
        try:
            dt = parser.isoparse(record_time_str)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=tz.tzutc())
            return dt
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid record_time format: {record_time_str}")
            logger.error(f"Error parsing record_time: {e}")
            return None

def to_iso_z_format(dt: datetime) -> str:
    """
    将 datetime 对象转换为 ISO8601 字符串，并将 +00:00 替换为 Z。
    """
    rt_str = dt.isoformat()
    # 将UTC时区的 +00:00 替换为 Z，以避免URL解析问题
    rt_str = rt_str.replace('+00:00', 'Z')
    return rt_str

def supabase_select(table_name, anchor_id, record_time):
    rt_str = to_iso_z_format(record_time)
    url = f"{SUPABASE_URL}/rest/v1/{table_name}?anchor_id=eq.{anchor_id}&record_time=eq.{rt_str}"
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        if len(data) > 0:
            return data[0]
        else:
            return None
    else:
        logger.error(f"Failed to select from {table_name}. Status: {resp.status_code}, Response: {resp.text}")
        return None

def supabase_insert(table_name, anchor_id, record_time, file_id_col, file_id, bot_token=None):
    rt_str = to_iso_z_format(record_time)
    payload = {
        "anchor_id": anchor_id,
        "record_time": rt_str,
        file_id_col: file_id,
        "norecord": True
    }
    
    # 如果提供了bot_token，则添加到payload中
    if bot_token:
        payload["bot_token"] = bot_token
    
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    resp = requests.post(url, headers=headers, json=payload)
    if resp.status_code in [200, 201]:
        logger.info(f"Inserted new record into {table_name} with norecord=True for Anchor ID: {anchor_id} at {record_time}. {file_id_col.upper()}: {file_id}")
    else:
        logger.error(f"Failed to insert into {table_name}. Status: {resp.status_code}, Response: {resp.text}")

def supabase_update(table_name, anchor_id, record_time, file_id_col, file_id, bot_token=None):
    rt_str = to_iso_z_format(record_time)
    url = f"{SUPABASE_URL}/rest/v1/{table_name}?anchor_id=eq.{anchor_id}&record_time=eq.{rt_str}"
    payload = {file_id_col: file_id}
    
    # 如果提供了bot_token，则添加到payload中
    if bot_token:
        payload["bot_token"] = bot_token
    
    resp = requests.patch(url, headers=headers, json=payload)
    if resp.status_code in [200, 204]:
        logger.info(f"Updated {file_id_col} for Anchor ID: {anchor_id} at {record_time}. {file_id_col.upper()}: {file_id}")
    else:
        logger.error(f"Failed to update {table_name}. Status: {resp.status_code}, Response: {resp.text}")

def update_tsid_record(anchor_id, tsid, record_time, bot_token=None):
    if not is_valid_uuid(anchor_id):
        logger.error(f"Invalid anchor_id format: {anchor_id}")
        return
    if record_time is None:
        logger.error(f"Record time is None for tsid: {tsid}")
        return

    existing_record = supabase_select(TSID_TABLE, anchor_id, record_time)
    if existing_record:
        supabase_update(TSID_TABLE, anchor_id, record_time, "tsid", tsid, bot_token)
    else:
        supabase_insert(TSID_TABLE, anchor_id, record_time, "tsid", tsid, bot_token)

def update_mp4id_record(anchor_id, mp4id, record_time, bot_token=None):
    if not is_valid_uuid(anchor_id):
        logger.error(f"Invalid anchor_id format: {anchor_id}")
        return
    if record_time is None:
        logger.error(f"Record time is None for mp4id: {mp4id}")
        return

    existing_record = supabase_select(MP4ID_TABLE, anchor_id, record_time)
    if existing_record:
        supabase_update(MP4ID_TABLE, anchor_id, record_time, "mp4id", mp4id, bot_token)
    else:
        supabase_insert(MP4ID_TABLE, anchor_id, record_time, "mp4id", mp4id, bot_token)

def process_id_message(message):
    try:
        data = message.decode('utf-8')
        logger.info(f"Received id message: {data}")
        fields = data.strip().split(',')
        logger.debug(f"Parsed fields: {fields}")
        
        # 处理包含bot_token的消息 (5个字段)
        if len(fields) == 5:
            anchor_id_str, file_id, id_type, record_time_str, bot_token = fields
            anchor_id = anchor_id_str.strip()
            file_id = file_id.strip()
            id_type = id_type.strip()
            record_time_str = record_time_str.strip()
            bot_token = bot_token.strip()
            logger.debug(f"anchor_id: {anchor_id}, file_id: {file_id}, id_type: {id_type}, record_time: {record_time_str}, bot_token: {bot_token}")
            record_time = parse_record_time(record_time_str)
            if id_type == 'mp4id':
                update_mp4id_record(anchor_id, file_id, record_time, bot_token)
            elif id_type == 'tsid':
                update_tsid_record(anchor_id, file_id, record_time, bot_token)
            else:
                logger.warning(f"Invalid id type: {id_type}")
        # 处理兼容旧版的消息 (4个字段，不包含bot_token)
        elif len(fields) == 4:
            anchor_id_str, file_id, id_type, record_time_str = fields
            anchor_id = anchor_id_str.strip()
            file_id = file_id.strip()
            id_type = id_type.strip()
            record_time_str = record_time_str.strip()
            logger.debug(f"anchor_id: {anchor_id}, file_id: {file_id}, id_type: {id_type}, record_time: {record_time_str}")
            record_time = parse_record_time(record_time_str)
            if id_type == 'mp4id':
                update_mp4id_record(anchor_id, file_id, record_time)
            elif id_type == 'tsid':
                update_tsid_record(anchor_id, file_id, record_time)
            else:
                logger.warning(f"Invalid id type: {id_type}")
        else:
            logger.warning(f"Invalid id message format: {data}")
    except Exception as e:
        logger.error(f"Error processing id message: {str(e)}")
        logger.error(f"Invalid id message data: {data}")

def main():
    pubsub = r.pubsub()
    pubsub.subscribe('id')
    logger.info("Starting id message processor...")

    try:
        for message in pubsub.listen():
            if message['type'] == 'message':
                channel = message['channel'].decode('utf-8')
                if channel == 'id':
                    process_id_message(message['data'])
    except Exception as e:
        logger.error(f"Error in id processor: {str(e)}")

if __name__ == '__main__':
    main()
