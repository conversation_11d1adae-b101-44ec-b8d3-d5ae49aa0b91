#!/bin/bash

# 检查Ubuntu版本
ubuntu_version=$(lsb_release -rs)
echo "检测到Ubuntu版本: $ubuntu_version"

# 更新系统
echo "正在更新系统..."
sudo apt update
sudo apt upgrade -y

# 安装 nload
echo "安装 nload..."
sudo apt install nload -y

# 安装 ffmpeg
echo "安装 ffmpeg..."
sudo apt install ffmpeg -y

# 安装 python3-pip
echo "安装 python3-pip..."
sudo apt install python3-pip -y

# 安装 python3-venv (对于 Ubuntu 24.04 及以上版本)
if (( $(echo "$ubuntu_version >= 24.04" | bc -l) )); then
    echo "安装 python3-venv..."
    sudo apt install python3-venv -y
    
    # 创建虚拟环境
    echo "创建并激活Python虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
fi

# 安装 streamlink
echo "安装 streamlink..."
pip3 install --user -U streamlink

# 配置环境变量
echo "配置环境变量..."
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc && source ~/.bashrc

# 安装 NVM
echo "安装 NVM..."
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash

# 配置 NVM 环境变量
echo "配置 NVM 环境变量..."
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 安装指定版本的 Node.js
echo "安装 Node.js v12.22.9..."
nvm install v12.22.9
nvm use v12.22.9
nvm alias default v12.22.9

# 安装最新版 Redis
echo "安装 Redis..."
curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/redis.list
sudo apt update
sudo apt install redis -y

# 启动 Redis 服务
echo "启动 Redis..."
sudo service redis-server start
sudo systemctl enable redis-server

# 安装 Python 依赖
echo "安装 Python 依赖..."
if (( $(echo "$ubuntu_version >= 24.04" | bc -l) )); then
    # 在虚拟环境中安装依赖
    pip install -r requirements.txt
    pip install python-dateutil
else
    # 直接安装依赖
    pip3 install --user -r requirements.txt
    pip3 install python-dateutil
fi

# 安装 Node.js 依赖
echo "安装 Node.js 依赖..."
npm install

# 验证安装
echo "验证安装..."

# 验证 nload
if command -v nload >/dev/null 2>&1; then
    echo "nload 安装成功！"
else
    echo "nload 可能没有正确安装，请检查。"
fi

# 验证 Redis
if redis-cli ping | grep -q "PONG"; then
    echo "Redis 安装成功！"
else
    echo "Redis 可能没有正确运行，请检查。"
fi

# 验证 Node.js 安装
echo "验证 Node.js 安装..."
if node -v | grep -q "v12.22.9"; then
    echo "Node.js 安装成功！"
else
    echo "Node.js 可能没有正确安装，请检查。"
fi

# 验证 ffmpeg 安装
echo "验证 ffmpeg 安装..."
if command -v ffmpeg >/dev/null 2>&1; then
    echo "ffmpeg 安装成功！"
else
    echo "ffmpeg 可能没有正确安装，请检查。"
fi

echo "安装完成！"

# 如果在虚拟环境中，显示提示信息
if (( $(echo "$ubuntu_version >= 24.04" | bc -l) )); then
    echo "注意：Python依赖已安装在虚拟环境中。"
    echo "使用时请先激活虚拟环境："
    echo "source venv/bin/activate"
fi