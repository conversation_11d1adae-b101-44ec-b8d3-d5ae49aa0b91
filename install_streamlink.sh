#!/bin/bash
set -e

check_architecture() {
    arch=$(uname -m)
    case $arch in
        x86_64)
            echo "x86_64"
            ;;
        aarch64)
            echo "aarch64"
            ;;
        i686)
            echo "i686"
            ;;
        *)
            echo "Unsupported architecture: $arch"
            exit 1
            ;;
    esac
}

get_latest_version() {
    latest_version=$(curl -s https://api.github.com/repos/streamlink/streamlink/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')
    if [ -z "$latest_version" ]; then
        echo "Failed to get latest version"
        exit 1
    fi
    echo "$latest_version"
}

download_streamlink() {
    local version=$1
    local arch=$2
    local filename="streamlink-${version:1}-1-cp311-cp311-manylinux2014_${arch}.AppImage"
    local url="https://github.com/streamlink/streamlink/releases/download/${version}/${filename}"
    
    echo "Downloading Streamlink ${version} for ${arch}..."
    if ! curl -L "$url" -o "/tmp/${filename}"; then
        echo "Failed to download Streamlink"
        exit 1
    fi
    echo "${filename}"
}

install_streamlink() {
    local arch=$(check_architecture)
    local version=$(get_latest_version)
    local filename
    
    filename=$(download_streamlink "$version" "$arch")
    if [ ! -f "/tmp/${filename}" ]; then
        echo "Download failed"
        exit 1
    fi
    
    # Make executable
    chmod +x "/tmp/${filename}"
    
    # Move to /usr/local/bin
    sudo mkdir -p /usr/local/bin
    sudo mv "/tmp/${filename}" "/usr/local/bin/streamlink.AppImage"
    
    # Create symlink
    sudo ln -sf /usr/local/bin/streamlink.AppImage /usr/local/bin/streamlink
    
    if command -v streamlink >/dev/null 2>&1; then
        echo "Streamlink ${version} has been installed successfully!"
        streamlink --version
    else
        echo "Installation failed"
        exit 1
    fi
}

# Check if system has required dependencies
check_dependencies() {
    local deps=("curl")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done
    
    if [ ${#missing[@]} -ne 0 ]; then
        echo "Missing required dependencies: ${missing[*]}"
        echo "Installing dependencies..."
        sudo apt update && sudo apt install -y "${missing[@]}"
    fi
}

main() {
    check_dependencies
    install_streamlink
}

main