const { createClient } = require('@supabase/supabase-js');
const Redis = require('ioredis');

const url = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';
const supabase = createClient(url, key);

const redis = new Redis({
  host: 'localhost',  
  port: 6379,
  db: 0,
});

const getCurrentTime = () => {
  return new Date().toLocaleString();
}

const updateOrderTime = async (anchorId) => {
  const { error } = await supabase
    .from('anchors')
    .update({ ordertime: new Date().toISOString() })
    .eq('anchor_id', anchorId);

  if (error) {
    console.error(`[${getCurrentTime()}] Error updating ordertime for anchor ${anchorId}: ${error}`);
  } else {
    console.log(`[${getCurrentTime()}] Updated ordertime for anchor ${anchorId}`);   
  }
};

const incrementCount = async (anchorId, countType) => {
  const { data, error } = await supabase
    .from('anchors')
    .select(countType)
    .eq('anchor_id', anchorId)
    .single();

  if (error) {
    console.error(`[${getCurrentTime()}] Error fetching ${countType} for anchor ${anchorId}: ${error}`);
    return;
  }

  const currentCount = data[countType] || 0;
  const newCount = currentCount + 1;

  const { error: updateError } = await supabase
    .from('anchors')  
    .update({ [countType]: newCount })
    .eq('anchor_id', anchorId);

  if (updateError) {
    console.error(`[${getCurrentTime()}] Error incrementing ${countType} for anchor ${anchorId}: ${updateError}`);
  } else {
    console.log(`[${getCurrentTime()}] Incremented ${countType} for anchor ${anchorId} to ${newCount}`);    
  }
};

const onStatusChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;
  
  if (newRecord.nosub === false && newRecord.status === true && oldRecord.status === false) {
    const anchorId = newRecord.anchor_id;
    
    console.log(`[${getCurrentTime()}] Status changed to true for record: ${JSON.stringify(newRecord)}`);

    const recordMessage = `start|${newRecord.anchor_id}|${newRecord.stream_url}|${newRecord.new_url}`;
    console.log(`[${getCurrentTime()}] 发送录制消息: ${recordMessage}`);

    redis.publish('record', recordMessage);

    await updateOrderTime(anchorId);
    await incrementCount(anchorId, 'acount');
  }
};

const onInsert = async (payload) => {
  const newRecord = payload.new;

  if (newRecord.nosub === false) {
    console.log(`[${getCurrentTime()}] New record inserted: ${JSON.stringify(newRecord)}`);
    
    const anchorId = newRecord.anchor_id;
    const liveUrl = newRecord.live_url;
    const platform = newRecord.platform; 
        
    const message = `${anchorId} ${liveUrl} ${platform}`;
    console.log(`[${getCurrentTime()}] 要传递的消息: ${message}`);

    redis.publish('new', message);
  }
};  

const onScanallChange = async (payload) => {
  console.log(`[${getCurrentTime()}] 执行全局扫描...`);
  
  const newRecord = payload.new;
  const oldRecord = payload.old;

  if (newRecord.scanall !== oldRecord.scanall) {
    const { data: anchors, error } = await supabase
      .from('anchors')  
      .select('anchor_id, new_url, platform')
      .eq('nosub', false);
    
    if (error) {
      console.error(`[${getCurrentTime()}] Error fetching anchors data: ${error}`);
      return; 
    }
    
    let dataCounter = 0;
    
    for (const anchor of anchors) {
      const message = `${anchor.anchor_id} ${anchor.new_url} ${anchor.platform}`;
      console.log(`[${getCurrentTime()}] 要传递的消息 ${++dataCounter}: ${message}`);
      redis.publish('new', message); 
    }
      
    console.log(`[${getCurrentTime()}] 批量传输完成,共传输 ${dataCounter} 行数据`);

    await checkAndRestartMissedRecordings();
  }
};

const checkAndRestartMissedRecordings = async () => {
  let { data: missedRecordings, error } = await supabase
    .from('anchors')
    .select('anchor_id, stream_url, new_url, finishtime, ordertime') 
    .eq('status', true)
    .eq('is_recording', false)
    .eq('nosub', false);
    
  if (error) {
    console.error(`[${getCurrentTime()}] Error fetching missed recordings: ${error}`);
    return;
  }

  for (const recording of missedRecordings) {
    let shouldResend = false;
    let reason = '';
    const currentTime = new Date();
      
    if (recording.finishtime) {
      const finishTime = new Date(recording.finishtime);
      const timeDiff = currentTime - finishTime;
      const minutes = Math.floor(timeDiff / 60000);
      const seconds = ((timeDiff % 60000) / 1000).toFixed(0);
        
      if (minutes >= 3) {
        shouldResend = true;
        reason = `finishtime超过3分钟 (距离现在: ${minutes}分${seconds}秒, 当前时间: ${currentTime.toISOString()}, 完成时间: ${finishTime.toISOString()})`;
      }
    } else if (recording.ordertime) {
      const orderTime = new Date(recording.ordertime); 
      const timeDiff = currentTime - orderTime;
      const minutes = Math.floor(timeDiff / 60000);
      const seconds = ((timeDiff % 60000) / 1000).toFixed(0);
        
      if (minutes >= 3) {
        shouldResend = true;
        reason = `ordertime超过3分钟 (距离现在: ${minutes}分${seconds}秒, 当前时间: ${currentTime.toISOString()}, 订单时间: ${orderTime.toISOString()})`;        
      }
    }  
      
    if (shouldResend) {      
      const recordMessage = `start|${recording.anchor_id}|${recording.stream_url}|${recording.new_url}`;
      console.log(`[${getCurrentTime()}] 发送遗漏的录制消息: ${recordMessage}`);
      console.log(`[${getCurrentTime()}] 原因: ${reason} (Anchor ID: ${recording.anchor_id})`);

      await updateOrderTime(recording.anchor_id);
      await incrementCount(recording.anchor_id, 'bcount');

      redis.publish('record', recordMessage);
    } 
  }
};

const sendTrueStatusToRecord = async () => {
  console.log(`[${getCurrentTime()}] 发送所有 status 为 true 且 nosub 为 false 的条目到 record 频道...`);
   
  const { data, error } = await supabase  
    .from('anchors')
    .select('anchor_id, stream_url, new_url, status')
    .eq('status', true)
    .eq('nosub', false);
    
  if (error) {
    console.error(`[${getCurrentTime()}] 获取 anchors 数据时出错: ${error}`);
    return;
  }
    
  for (const record of data) {
    const recordMessage = `start|${record.anchor_id}|${record.stream_url}|${record.new_url}`;
    console.log(`[${getCurrentTime()}] 发送录制消息: ${recordMessage}`);
     
    await updateOrderTime(record.anchor_id);  
    await incrementCount(record.anchor_id, 'acount');
    
    redis.publish('record', recordMessage);
  }
  
  console.log(`[${getCurrentTime()}] 所有 status 为 true 且 nosub 为 false 的条目已发送到 record 频道`);  
};

supabase  
  .channel('anchors-update')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'anchors',
    },
    onStatusChange
  )
  .subscribe();
  
supabase
  .channel('anchors-insert')   
  .on(
    'postgres_changes',
    {
      event: 'INSERT', 
      schema: 'public',
      table: 'anchors',
    },
    onInsert 
  )
  .subscribe();

supabase
  .channel('does-update')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public', 
      table: 'does',
    },
    onScanallChange   
  )
  .subscribe();

const args = process.argv.slice(2);
const shouldSendTrueStatus = args[0] === '1';

if (shouldSendTrueStatus) {
  sendTrueStatusToRecord().then(() => {
    console.log(`[${getCurrentTime()}] 初始化操作完成`);  
  });
} else {
  console.log(`[${getCurrentTime()}] 跳过初始化操作`);
}

console.log(`[${getCurrentTime()}] 程序启动,开始监听数据库变更`);