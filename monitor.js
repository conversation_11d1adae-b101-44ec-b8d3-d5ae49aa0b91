const fs = require('fs');
const path = require('path');
const Redis = require('ioredis');
const { exec } = require('child_process');

// 配置部分
const RECORDING_DIR = './recordings'; // 录制文件的根目录
const REDIS_POST_STREAM = 'post_stream'; // Redis Stream 的名称
const SCAN_INTERVAL = 72000000; // 扫描间隔，毫秒（20 小时）
const FILE_AGE_THRESHOLD = 36000; // 文件时间阈值，秒（10 小时）
const WIREGUARD_TOGGLE_INTERVAL = 72000000; // WireGuard 切换间隔，毫秒（20 小时）

// 功能开关
const SCANNING_ENABLED = true; // 控制是否执行扫描录制目录的功能
const WIREGUARD_TOGGLE_ENABLED = false; // 控制是否执行 WireGuard 切换的功能

// 处理启动参数
const IGNORE_FILE_AGE_ON_STARTUP = process.argv[2] === '1'; // 如果启动参数为 '1'，则忽略文件时间阈值

const redis = new Redis();

// 获取当前 Unix 时间戳（秒）
const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);

// 将 Unix 时间戳转换为 ISO 格式字符串
const timestampToISO = (timestamp) => new Date(timestamp * 1000).toISOString();

// 将秒转换为可读的时间格式
const formatTimeDifference = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h}小时${m}分钟${s}秒`;
};

// 检查 WireGuard 是否正在运行
const isWireGuardRunning = () => {
  return new Promise((resolve, reject) => {
    exec('ip addr', (error, stdout, stderr) => {
      if (error) {
        console.error(`[${new Date().toLocaleString()}] 执行 'ip addr' 时出错：${error.message}`);
        return resolve(false);
      }
      // 检查输出中是否包含 'wgcf' 接口
      if (stdout.includes('wgcf')) {
        resolve(true); // WireGuard 正在运行
      } else {
        resolve(false); // WireGuard 未运行
      }
    });
  });
};

// 切换 WireGuard 状态
const toggleWireGuard = async () => {
  const running = await isWireGuardRunning();
  if (running) {
    console.log(`[${new Date().toLocaleString()}] WireGuard 正在运行，执行 'sudo ./warp.sh dwg' 关闭 WireGuard`);
    exec('sudo ./warp.sh dwg', (error, stdout, stderr) => {
      if (error) {
        console.error(`[${new Date().toLocaleString()}] 执行 'sudo ./warp.sh dwg' 时出错：${error.message}`);
        return;
      }
      console.log(`[${new Date().toLocaleString()}] 已成功关闭 WireGuard`);
    });
  } else {
    console.log(`[${new Date().toLocaleString()}] WireGuard 未运行，执行 'sudo ./warp.sh wg4' 开启 WireGuard`);
    exec('sudo ./warp.sh wg4', (error, stdout, stderr) => {
      if (error) {
        console.error(`[${new Date().toLocaleString()}] 执行 'sudo ./warp.sh wg4' 时出错：${error.message}`);
        return;
      }
      console.log(`[${new Date().toLocaleString()}] 已成功开启 WireGuard`);
    });
  }
};

// 启动 WireGuard 切换任务
const startWireGuardToggle = () => {
  if (WIREGUARD_TOGGLE_ENABLED) {
    console.log(`[${new Date().toLocaleString()}] 开始 WireGuard 切换任务，每隔 ${WIREGUARD_TOGGLE_INTERVAL / 3600000} 小时切换一次...`);

    // 立即执行一次切换
    toggleWireGuard();

    // 定期执行切换
    setInterval(toggleWireGuard, WIREGUARD_TOGGLE_INTERVAL);
  } else {
    console.log(`[${new Date().toLocaleString()}] WireGuard 切换任务已禁用。`);
  }
};

// 扫描录制目录的函数
const scanRecordings = async (ignoreFileAge = false) => {
  console.log(`[${new Date().toLocaleString()}] 开始扫描录制目录，${ignoreFileAge ? '忽略文件时间阈值，处理所有文件' : `查找存在超过 ${FILE_AGE_THRESHOLD / 3600} 小时的文件`}...`);

  try {
    // 读取 recordings 目录下的所有子目录（主播 ID）
    const anchorDirs = await fs.promises.readdir(RECORDING_DIR, { withFileTypes: true });
    for (const dirent of anchorDirs) {
      if (dirent.isDirectory()) {
        const anchorId = dirent.name;
        const anchorDirPath = path.join(RECORDING_DIR, anchorId);

        // 读取主播目录下的所有文件
        const files = await fs.promises.readdir(anchorDirPath);
        for (const file of files) {
          // 只监测 .ts 文件
          if (!file.endsWith('.ts')) {
            continue;
          }

          const filePath = path.join(anchorDirPath, file);

          // 解析文件名，提取 timestamp 和 anchor_id
          const match = file.match(/^(\d+)-([a-f0-9-]+)\.ts$/);
          if (!match) {
            console.warn(`[${new Date().toLocaleString()}] 文件名格式不正确，跳过：${file}`);
            continue;
          }

          const timestamp = parseInt(match[1], 10);
          const fileAnchorId = match[2];

          if (isNaN(timestamp) || !fileAnchorId || fileAnchorId !== anchorId) {
            console.warn(`[${new Date().toLocaleString()}] 文件名内容不匹配，跳过：${file}`);
            continue;
          }

          // 检查文件是否超过指定的时间阈值
          const currentTimestamp = getCurrentTimestamp();
          const fileAge = currentTimestamp - timestamp;

          if (ignoreFileAge || fileAge >= FILE_AGE_THRESHOLD) {
            // 准备发送的消息
            const recordTime = timestampToISO(timestamp);
            const message = {
              anchor_id: anchorId,
              file_path: filePath,
              recordtime: recordTime,
            };

            // 发送消息到 Redis Stream
            await redis.xadd(
              REDIS_POST_STREAM,
              '*',
              'anchor_id', message.anchor_id,
              'file_path', message.file_path,
              'recordtime', message.recordtime
            );

            const timeDifference = formatTimeDifference(fileAge);

            console.log(`[${new Date().toLocaleString()}] 已发送文件信息到 Redis Stream：${filePath}，文件已存在 ${timeDifference}`);
          }
        }
      }
    }
  } catch (err) {
    console.error(`[${new Date().toLocaleString()}] 扫描录制目录时出错：${err.message}`);
  }
};

// 启动定期扫描
const startScanning = () => {
  if (SCANNING_ENABLED) {
    console.log(`[${new Date().toLocaleString()}] 开始定期扫描录制目录，每隔 ${SCAN_INTERVAL / 3600000} 小时扫描一次。文件时间阈值为 ${FILE_AGE_THRESHOLD / 3600} 小时。`);

    // 立即执行一次扫描，根据启动参数决定是否忽略文件时间阈值
    scanRecordings(IGNORE_FILE_AGE_ON_STARTUP);

    // 每隔指定的间隔执行一次扫描，之后的扫描都考虑文件时间阈值
    setInterval(() => {
      scanRecordings(false);
    }, SCAN_INTERVAL);
  } else {
    console.log(`[${new Date().toLocaleString()}] 扫描录制目录任务已禁用。`);
  }
};

// 开始 WireGuard 切换任务
startWireGuardToggle();

// 开始扫描任务
startScanning();
