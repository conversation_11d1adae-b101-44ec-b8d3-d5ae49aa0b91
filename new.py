import redis
import json
import subprocess
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor
import logging
from datetime import datetime
from supabase import create_client, Client

# 依旧依赖外部函数的平台
from bytedance import get_douyin_live_info
from streamlink import get_live_info  # 用于 bilibili 的原始处理方式
from douyinliverecorder import spider, stream

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Redis 配置
r = redis.Redis(host='localhost', port=6379, db=0)

# Supabase 配置
SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# 公用配置
proxy_addr = None
bili_cookie = ""
tiktok_cookie = ""
record_quality = "原画"

def get_stream_info(url: str, cookies_file: str) -> Dict[str, Any]:
    """
    获取 YouTube 直播信息 (集成到本程序中，替代原先对 YouTube 的外部调用)

    Args:
        url: YouTube 直播间 URL
        cookies_file: cookies 文件路径 (程序当前目录下的 cookies.txt)

    Returns:
        包含直播信息的字典，包括：
        - anchor_name: 主播昵称/频道名称
        - is_live: 是否正在直播
        - record_url: 最高画质的直播流链接
        - title: 直播标题
    """
    try:
        # 使用 yt-dlp 获取 JSON 信息
        cmd = [
            'yt-dlp',
            '--cookies', cookies_file,
            '--dump-json',
            url
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"yt-dlp error: {result.stderr}")
        
        # 解析 JSON 数据
        data = json.loads(result.stdout)
        
        # 获取最高画质的流
        formats = data.get('formats', [])
        best_format = None
        max_height = 0
        for fmt in formats:
            height = fmt.get('height', 0)
            if height > max_height:
                max_height = height
                best_format = fmt
        
        info = {
            'anchor_name': data.get('channel', ''),
            'is_live': data.get('is_live', False),
            'record_url': best_format.get('url') if best_format else '',
            'title': data.get('title', '')
        }
        
        return info
        
    except Exception as e:
        logger.error(f"Error getting stream info from YouTube: {str(e)}")
        return {
            'anchor_name': '',
            'is_live': False,
            'record_url': '',
            'title': ''
        }

def process_live_info(platform, url, anchor_id):
    """
    根据平台获取直播信息。
    对 YouTube 使用本地的 get_stream_info 函数；
    其余平台依赖外部函数（保持不变）。
    """
    try:
        # ------------------- Douyin 逻辑（保持原样） -------------------
        if platform == "douyin":
            result = get_douyin_live_info(url, anchor_id)

        # ------------------- TikTok 逻辑（保持原样） -------------------
        elif platform == "tiktok":
            json_data = spider.get_tiktok_stream_data(url=url, proxy_addr=proxy_addr, cookies=tiktok_cookie)
            port_info = stream.get_tiktok_stream_url(json_data, record_quality)

            anchor_name = port_info.get('anchor_name', '')
            is_live = port_info.get('is_live', False)
            record_url = port_info.get('record_url', '')
            title = port_info.get('title', '')
            new_url = ''  # TikTok 有时会有新 URL，这里不做额外处理

            result_dict = {
                "anchor_id": anchor_id,
                "anchor_name": anchor_name,
                "stream_url": record_url,
                "status": is_live,
                "title": title,
                "new_url": new_url
            }
            result = result_dict

        # ------------------- Twitch 逻辑（保持原样） -------------------
        elif platform == "twitch":
            json_data = spider.get_twitchtv_stream_data(url=url, proxy_addr=proxy_addr, cookies=None)
            port_info = stream.get_stream_url(json_data, record_quality, spec=True)

            anchor_name = port_info.get('anchor_name', '')
            is_live = port_info.get('is_live', False)
            record_url = port_info.get('record_url', '')
            title = port_info.get('title', '')
            new_url = ''  # Twitch 不使用 new_url

            result_dict = {
                "anchor_id": anchor_id,
                "anchor_name": anchor_name,
                "stream_url": record_url,
                "status": is_live,
                "title": title,
                "new_url": new_url
            }
            result = result_dict

        # ------------------- Youtube 逻辑（使用内置 get_stream_info） -------------------
        elif platform == "youtube":
            # 使用我们新集成的函数
            info = get_stream_info(url, 'cookies.txt')
            anchor_name = info.get('anchor_name', '')
            is_live = info.get('is_live', False)
            record_url = info.get('record_url', '')
            title = info.get('title', '')

            # 注意：YouTube 平台没有 new_url
            result_dict = {
                "anchor_id": anchor_id,
                "anchor_name": anchor_name,
                "stream_url": record_url,
                "status": is_live,
                "title": title,
                "new_url": ""
            }
            result = result_dict

        # ------------------- Bilibili 逻辑（保持原样） -------------------
        elif platform == "bilibili":
            result = get_live_info(platform, url, anchor_id)

        else:
            raise ValueError(f"不支持的平台: {platform}")

        # ------------------- 通用结果处理 -------------------
        if isinstance(result, str):
            result_dict = json.loads(result)
        elif isinstance(result, dict):
            result_dict = result
        else:
            raise ValueError(f"无法解析的结果类型: {type(result)}")

        anchor_id = result_dict.get('anchor_id', '')
        anchor_name = result_dict.get('anchor_name', '')
        stream_url = result_dict.get('stream_url', '')
        status = result_dict.get('status', False)
        title = result_dict.get('title', '')
        new_url = result_dict.get('new_url', '')

        # 进一步约束：对 YouTube / Bilibili / Twitch 不更新 new_url
        if platform in ["youtube", "bilibili", "twitch"]:
            new_url = ''

        return {
            'anchor_id': anchor_id,
            'anchor_name': anchor_name,
            'stream_url': stream_url,
            'status': status,
            'title': title,
            'new_url': new_url
        }

    except Exception as e:
        logger.error(f"解析 {platform} 链接失败: {url}, 错误信息: {e}")
        return {'error': str(e), 'anchor_id': anchor_id}

def update_anchor_info(message_data_list):
    """
    接收处理后的直播信息列表，更新数据库 anchors 表。
    对于 new_url 为空或属于 YouTube/Bilibili/Twitch，不更新 new_url 字段。
    """
    for message_data in message_data_list:
        anchor_id = message_data.get('anchor_id')
        anchor_name = message_data.get('anchor_name')
        stream_url = message_data.get('stream_url')
        status = message_data.get('status')
        title = message_data.get('title')
        new_url = message_data.get('new_url')

        update_data = {}
        if anchor_name:
            update_data["anchor_name"] = anchor_name
        if stream_url:
            update_data["stream_url"] = stream_url
        if status is not None:
            update_data["status"] = status
        if title:
            update_data["title"] = title

        # 只有在 new_url 不为空且不属于 [youtube, bilibili, twitch] 才更新
        # 因为在 process_live_info 中已经对 youtube/bilibili/twitch 设置为 ''
        if new_url:
            update_data["new_url"] = new_url

        if update_data:
            response = supabase.table("anchors").update(update_data).eq("anchor_id", anchor_id).execute()
            if response.status_code not in (200, 204):
                logger.error(f"Failed to update anchor info for {anchor_id}: {response.data}")
            else:
                logger.info(f"Successfully updated anchor info for {anchor_id}")

def process_message(message):
    """
    从 Redis 订阅消息中获取 anchor_id、live_url、platform，并调用处理流程。
    """
    if message['type'] == 'message':
        data = message['data'].decode('utf-8')
        anchor_id, live_url, platform = data.split(' ')

        result = process_live_info(platform, live_url, anchor_id)

        logger.info(f"Received message: {data}")
        logger.info(f"Processed result: {result}")

        update_anchor_info([result])

def main():
    """
    主函数：订阅 Redis 频道 'new' 并在有新消息时处理
    """
    pubsub = r.pubsub()
    pubsub.subscribe('new')

    with ThreadPoolExecutor() as executor:
        for message in pubsub.listen():
            executor.submit(process_message, message)

if __name__ == "__main__":
    main()
