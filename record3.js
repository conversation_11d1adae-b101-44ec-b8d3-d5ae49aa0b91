const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const Redis = require('ioredis');
const { URL } = require('url');
const { exec } = require('child_process');
const util = require('util');
const yaml = require('js-yaml'); // 需要添加依赖: npm install js-yaml

// 将exec转换为Promise版本
const execPromise = util.promisify(exec);

// 日志级别定义
const LOG_LEVEL = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 当前使用的日志级别，可在配置文件中修改
const CURRENT_LOG_LEVEL = LOG_LEVEL.INFO;

/**
 * 日志工具函数，根据级别过滤输出
 * @param {number} level 日志级别
 * @param {string} message 日志消息
 * @param {string} anchorId 可选，关联主播ID
 */
function log(level, message, anchorId = '') {
  if (level >= CURRENT_LOG_LEVEL) {
    const anchorInfo = anchorId ? `[anchor_id: ${anchorId}] ` : '';
    console.log(`[${getCurrentTime()}] ${anchorInfo}${message}`);
  }
}

/**
 * 错误日志工具函数
 * @param {string} message 错误消息
 * @param {Error} error 错误对象
 * @param {string} anchorId 可选，关联主播ID
 */
function logError(message, error, anchorId = '') {
  if (LOG_LEVEL.ERROR >= CURRENT_LOG_LEVEL) {
    const anchorInfo = anchorId ? `[anchor_id: ${anchorId}] ` : '';
    console.error(`[${getCurrentTime()}] ${anchorInfo}${message}: ${error.message}`);
  }
}

/**
 * API调用错误处理包装函数，支持重试
 * @param {Function} operation 异步操作函数
 * @param {number} maxRetries 最大重试次数
 * @param {string} operationName 操作名称，用于日志
 * @param {string} anchorId 可选，关联主播ID
 * @returns {Promise<any>} 操作结果
 */
async function withRetry(operation, maxRetries = 3, operationName = 'API调用', anchorId = '') {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (err) {
      // 某些错误永远不会成功，不需要重试
      if (attempt === maxRetries - 1 || 
          err.message.includes('Permission') || 
          err.message.includes('not found') ||
          err.message.includes('权限不足')) {
        throw err;
      }
        
      log(LOG_LEVEL.WARN, `${operationName}失败，${attempt+1}/${maxRetries}次尝试, 等待重试...`, anchorId);
      await new Promise(r => setTimeout(r, 1000 * (attempt + 1)));
    }
  }
}

/**
 * 安全文件操作包装函数
 * @param {Function} operation 文件操作函数
 * @param {string} errorMessage 错误消息
 * @param {string} anchorId 可选，关联主播ID
 * @returns {Promise<any>} 操作结果
 */
async function safeFileOperation(operation, errorMessage, anchorId = '') {
  try {
    return await operation();
  } catch (err) {
    if (err.code !== 'ENOENT') {
      logError(errorMessage, err, anchorId);
    }
    return null;
  }
}

// PROGRAM_TAG 用于在 does 表中查找对应字段获取区间
const PROGRAM_TAG = 'jp';

// 配置部分
const url = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';

const supabase = createClient(url, key);
const RECORDING_DIR = './recordings';
const REDIS_POST_STREAM = 'post_stream';

const redis = new Redis();

// 全局记录所有活跃的录制任务
// 每个 key: anchor_id, value: { ytdlpProcess, recordingPromise, anchorInfo: { anchor_name, platform }, latestOutputFile, taskRecordTime, isStopping }
const activeRecordings = new Map();

// 新增 Map cleanupDone 记录各录制是否完整执行尾流程
const cleanupDone = new Map();

// 用于记录每个主播最后使用的URL，避免重复使用相同的无效链接
const lastUsedUrls = new Map();

// 用于跟踪status=false但is_recording=true的主播次数
const abnormalRecordingCounts = new Map();

// 用于存储当前区间的下限和上限（offset 与 limit）
let currentOffset = 0;
let currentLimit = 0; // limit = end - start

// 新增: 单个录制文件大小限制 (1.95 GiB)
const FILE_SIZE_LIMIT_BYTES = Math.floor(1.95 * 1024 * 1024 * 1024);

/**
 * 获取当前时间字符串
 */
const getCurrentTime = () => {
  return new Date().toLocaleString();
};

/**
 * 从 does 表中读取对应 PROGRAM_TAG 字段的范围，并更新 currentOffset/currentLimit
 */
async function updateRangeFromDoes() {
  try {
    const { data, error } = await supabase
      .from('does')
      .select(`${PROGRAM_TAG}, scanall`)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 从 does 表读取范围时出错: ${error.message}`);
      return;
    }

    const rangeStr = data[PROGRAM_TAG]; // 例如 "[100,550)"
    const match = rangeStr.match(/[\[(](\d+),(\d+)[\])]/);
    if (!match) {
      console.error(`[${getCurrentTime()}] does表中${PROGRAM_TAG}字段的范围格式不正确: ${rangeStr}`);
      return;
    }

    const start = parseInt(match[1], 10);
    const end = parseInt(match[2], 10);
    currentOffset = start;
    currentLimit = end - start;

    console.log(`[${getCurrentTime()}] 当前作用范围: ${rangeStr}, offset=${currentOffset}, limit=${currentLimit}`);
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新范围时发生异常: ${err.message}`);
  }
}

/**
 * 更新 anchors 表中指定 anchor_id 的指定字段
 */
async function updateAnchorStatus(anchorId, updates) {
  try {
    // 先获取主播名称
    let anchorName = '';
    try {
      const { data, error } = await supabase
        .from('anchors')
        .select('anchor_name')
        .eq('anchor_id', anchorId)
        .single();
      
      if (!error && data) {
        anchorName = data.anchor_name || '';
      }
    } catch (err) {
      // 即使获取名称失败也继续更新状态
      console.error(`[${getCurrentTime()}] 获取主播 ${anchorId} 的名称时出错: ${err.message}`);
    }

    const { error } = await supabase
      .from('anchors')
      .update(updates)
      .eq('anchor_id', anchorId);

    if (error) {
      console.error(`[${getCurrentTime()}] 更新主播 ${anchorId}${anchorName ? `(${anchorName})` : ''} 的状态时出错: ${error.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 成功更新主播 ${anchorId}${anchorName ? `(${anchorName})` : ''} 的状态: ${JSON.stringify(updates)}`);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新主播状态时发生异常: ${err.message}`);
  }
}

/**
 * 在 anchor_mp4id、anchor_tsid 表分别插入 record_time，
 * 并在 anchors 表中更新 recordtime 字段
 */
async function insertRecordTime(anchorId, recordTime) {
  try {
    // 1. 插入到 anchor_mp4id
    const { error: errorMp4 } = await supabase
      .from('anchor_mp4id')
      .insert({ anchor_id: anchorId, mp4id: '', record_time: recordTime });

    if (errorMp4) {
      console.error(`[${getCurrentTime()}] 插入到 anchor_mp4id 时出错: ${errorMp4.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 在 anchor_mp4id 中插入记录 (anchor_id: ${anchorId}) 成功`);
    }

    // 2. 插入到 anchor_tsid
    const { error: errorTs } = await supabase
      .from('anchor_tsid')
      .insert({ anchor_id: anchorId, tsid: '', record_time: recordTime });

    if (errorTs) {
      console.error(`[${getCurrentTime()}] 插入到 anchor_tsid 时出错: ${errorTs.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 在 anchor_tsid 中插入记录 (anchor_id: ${anchorId}) 成功`);
    }

    // 3. 更新 anchors 表中对应行的 recordtime 字段
    const { error: errorAnchors } = await supabase
      .from('anchors')
      .update({ recordtime: recordTime })
      .eq('anchor_id', anchorId);

    if (errorAnchors) {
      console.error(`[${getCurrentTime()}] 在 anchors 表更新 recordtime 时出错: ${errorAnchors.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 在 anchors 表更新 recordtime (anchor_id: ${anchorId}) 成功`);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 插入 record_time 时发生异常: ${err.message}`);
  }
}

/**
 * 增加计数
 */
async function incrementCount(anchorId, countType) {
  try {
    const { data, error } = await supabase
      .from('anchors')
      .select(countType)
      .eq('anchor_id', anchorId)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 获取主播 ${anchorId} 的 ${countType} 时出错: ${error.message}`);
      return;
    }

    const currentCount = data[countType] || 0;
    const newCount = currentCount + 1;

    const { error: updateError } = await supabase
      .from('anchors')
      .update({ [countType]: newCount })
      .eq('anchor_id', anchorId);

    if (updateError) {
      console.error(`[${getCurrentTime()}] 增加主播 ${anchorId} 的 ${countType} 时出错: ${updateError.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 主播 ${anchorId} 的 ${countType} 增加到 ${newCount}`);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 增加计数时发生异常: ${err.message}`);
  }
}

/**
 * 当 does 表的 PROGRAM_TAG 字段或 scanall 字段发生变化时，更新范围并检查遗漏录制
 */
const onDoesChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  let rangeChanged = false;
  let scanallChanged = false;

  if (oldRecord[PROGRAM_TAG] !== newRecord[PROGRAM_TAG]) {
    log(LOG_LEVEL.INFO, `does 表的 ${PROGRAM_TAG} 字段发生变化，更新区间范围...`);
    rangeChanged = true;
  }

  if (oldRecord.scanall !== newRecord.scanall) {
    log(LOG_LEVEL.INFO, `does表的scanall字段发生变化，触发对遗漏录制的检查`);
    log(LOG_LEVEL.INFO, `当前scanall: ${oldRecord.scanall} -> ${newRecord.scanall}`);
    
    // 打印当前正在录制中的条目（只显示 anchor_id、anchor_name 和 platform）
    if (activeRecordings.size === 0) {
      log(LOG_LEVEL.INFO, `目前本机没有正在录制的条目`);
    } else {
      log(LOG_LEVEL.INFO, `本机实际正在录制的主播列表(${activeRecordings.size}个)：`);
      let idx = 1;
      for (const [anchorId, { anchorInfo }] of activeRecordings.entries()) {
        log(LOG_LEVEL.INFO, 
          `${idx++}. anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}`
        );
      }
    }
    
    // 查询并显示当前所有录制状态
    try {
      // 1. 查询当前区间内所有主播的状态
      const { data: allAnchors, error: queryError } = await supabase
        .from('anchors')
        .select('anchor_id, anchor_name, platform, status, is_recording, finishtime, ordertime')
        .range(currentOffset, currentOffset + currentLimit - 1);

      if (queryError) {
        log(LOG_LEVEL.ERROR, `查询主播状态时出错: ${queryError.message}`);
      } else if (allAnchors) {
        // 分类统计
        const statusTrue = allAnchors.filter(a => a.status === true);
        const statusTrueNotRecording = statusTrue.filter(a => a.is_recording === false);
        
        // 显示status=true但未录制的主播
        if (statusTrueNotRecording.length > 0) {
          log(LOG_LEVEL.INFO, `数据库中status=true但未录制的主播(${statusTrueNotRecording.length}个):`);
          statusTrueNotRecording.forEach((anchor, idx) => {
            let timeInfo = "";
            const currentTime = new Date();
            
            if (anchor.finishtime) {
              const finishTime = new Date(anchor.finishtime);
              const timeDiff = Math.round((currentTime - finishTime) / 1000);
              timeInfo = `距finishtime: ${timeDiff}秒`;
            } else if (anchor.ordertime) {
              const orderTime = new Date(anchor.ordertime);
              const timeDiff = Math.round((currentTime - orderTime) / 1000);
              timeInfo = `距ordertime: ${timeDiff}秒`;
            } else {
              timeInfo = "无时间记录";
            }
            
            log(LOG_LEVEL.INFO, `${idx+1}. anchor_id: ${anchor.anchor_id}, anchor_name: ${anchor.anchor_name}, platform: ${anchor.platform}, ${timeInfo}`);
          });
        } else {
          log(LOG_LEVEL.INFO, `没有发现status=true但未录制的主播`);
        }
        
        // 显示状态摘要
        log(LOG_LEVEL.INFO, `当前录制状态摘要:
        - 区间内总主播数: ${allAnchors.length}
        - status=true的主播: ${statusTrue.length}
        - 实际正在录制中: ${activeRecordings.size}
        - 待录制主播数量: ${statusTrueNotRecording.length}`);
      }
    } catch (err) {
      log(LOG_LEVEL.ERROR, `查询录制状态时发生异常: ${err.message}`);
    }

    scanallChanged = true;
  }

  if (rangeChanged) {
    await updateRangeFromDoes();
    // 注意：rangeChanged 也要检查是否遗漏
    await checkAndRestartMissedRecordings();
  }

  if (scanallChanged) {
    // 如果scanall有变化，也要重新检查遗漏条目
    await checkAndRestartMissedRecordings();
  }
};

/**
 * 验证是否为合法URL
 */
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (err) {
    return false;
  }
}

/**
 * （可选）读取 YouTube cookies 的示例函数
 * 如果你需要YouTube录制才需要启用，具体实现见上一个示例
 */
function getYoutubeCookies(cookiesFilePath) {
  let youtubeCookiesString = '';
  try {
    const lines = fs.readFileSync(cookiesFilePath, 'utf-8').split('\n');
    const cookiesArr = [];

    for (const line of lines) {
      // 跳过注释、空行
      if (!line || line.startsWith('#')) continue;

      // Netscape cookies.txt 格式通常是
      // domain   includeSubdomains   path   httpsOnly   expiration   name   value
      // 例如：
      // .youtube.com    TRUE    /    FALSE    1692292191    SID    XXXXXXXXX
      const parts = line.trim().split(/\s+/);
      if (parts.length < 7) {
        continue;
      }

      const domain = parts[0];
      const name = parts[5];
      const value = parts[6];

      // 判断是否 YouTube 域名
      if (domain.includes('youtube.com')) {
        cookiesArr.push(`${name}=${value}`);
      }
    }

    if (cookiesArr.length > 0) {
      youtubeCookiesString = cookiesArr.join('; ');
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 读取cookie文件出错: ${err.message}`);
  }

  return youtubeCookiesString;
}

/**
 * 对指定 anchor_id 的链接开始录制
 */
async function startRecording(anchorId, streamUrlData) {
  try {
    // 解析stream_url数据
    let streamUrlObject;
    try {
      // 尝试将字符串解析为JSON对象
      if (typeof streamUrlData === 'string') {
        // 检查是否为简单的URL而不是JSON字符串
        if (streamUrlData.startsWith('http')) {
          // 如果是普通URL，创建简单的流对象
          streamUrlObject = { hls_pull_url: streamUrlData };
        } else {
          // 尝试解析JSON
          streamUrlObject = JSON.parse(streamUrlData);
        }
      } else if (typeof streamUrlData === 'object') {
        streamUrlObject = streamUrlData;
      } else {
        throw new Error(`无效的流数据类型: ${typeof streamUrlData}`);
      }
    } catch (err) {
      console.error(`[${getCurrentTime()}] 解析流URL数据失败: ${err.message}, 数据: ${streamUrlData.substring(0, 100)}...`);
      return;
    }
    
    // 如果没有任何可用URL，跳过录制
    if (!streamUrlObject || 
        (!streamUrlObject.hls_pull_url && 
         !streamUrlObject.hls_pull_url_map && 
         !streamUrlObject.flv_pull_url)) {
      console.error(`[${getCurrentTime()}] 无可用的流URL: ${JSON.stringify(streamUrlObject).substring(0, 100)}...`);
      return;
    }

    // 先获取 anchor_name、platform 和 new_url 用于后续打印
    let anchorInfo = { anchor_name: '', platform: '', _new_url: '' };
    const { data: anchorData, error } = await supabase
      .from('anchors')
      .select('anchor_name, platform, new_url')
      .eq('anchor_id', anchorId)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 未能获取主播 ${anchorId} 的 anchor_name, platform: ${error.message}`);
    } else {
      anchorInfo = { 
        anchor_name: anchorData.anchor_name || '', 
        platform: anchorData.platform || '',
        _new_url: anchorData.new_url || ''
      };
    }

    // 如果stream_url对象中有_new_url，优先使用它
    if (streamUrlObject._new_url) {
      anchorInfo._new_url = streamUrlObject._new_url;
    }
    
    // 如果stream_url对象中有_anchor_name且anchorInfo的anchor_name为空，使用它
    if (streamUrlObject._anchor_name && !anchorInfo.anchor_name) {
      anchorInfo.anchor_name = streamUrlObject._anchor_name;
    }

    // 先在 activeRecordings 中创建一个占位条目
    activeRecordings.set(anchorId, { 
      ytdlpProcess: null, 
      recordingPromise: null, 
      anchorInfo, 
      isStopping: false, 
      latestOutputFile: null,
      taskRecordTime: null
    });

    // 改用 ffmpeg 版本的录制实现
    const { ytdlpProcess, recordingPromise, isStopping } = await recordStreamFfmpeg(streamUrlObject, anchorId, anchorInfo);
    
    // 更新activeRecordings中的详细信息
    if (activeRecordings.has(anchorId)) {
      const recording = activeRecordings.get(anchorId);
      recording.ytdlpProcess = ytdlpProcess;
      recording.recordingPromise = recordingPromise;
      recording.isStopping = isStopping;
    }

    recordingPromise
      .then(() => {
        activeRecordings.delete(anchorId);
      })
      .catch(async (err) => {
        console.error(
          `[${getCurrentTime()}] 录制过程中出现异常 (anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}): ${err.message}`
        );
        activeRecordings.delete(anchorId);
        await updateAnchorStatus(anchorId, { is_recording: false, finishtime: new Date().toISOString() });
      });
  } catch (err) {
    console.error(`[${getCurrentTime()}] 开始录制主播 ${anchorId} 时出错: ${err.message}`);
  }
}

/**
 * 从流对象中提取最佳可用URL
 * @param {object} streamObj 流对象
 * @param {string} quality 指定清晰度，为null时按优先级选择
 * @param {string} platform 平台标识
 * @returns {string|null} URL或null
 */
function extractBestStreamUrl(streamObj, quality = null, platform = '') {
  // 如果传入的是简单URL字符串，且是HLS，直接返回
  if (typeof streamObj === 'string' && streamObj.startsWith('http') && (streamObj.includes('.m3u8') || streamObj.includes('/hls/'))) {
    return streamObj;
  } else if (typeof streamObj === 'string') {
    // 如果是字符串但不是HLS，则认为无效
    return null;
  }
  
  if (!streamObj) return null;
  
  // 处理嵌套在live_core_sdk_data.pull_data.stream_data中的JSON字符串
  if (streamObj.live_core_sdk_data && streamObj.live_core_sdk_data.pull_data && 
      streamObj.live_core_sdk_data.pull_data.stream_data) {
    try {
      const streamData = JSON.parse(streamObj.live_core_sdk_data.pull_data.stream_data);
      const backupUrls = [];
      
      // 只提取最高画质(origin)的HLS链接
      if (streamData.data && streamData.data.origin && streamData.data.origin.main && 
          streamData.data.origin.main.hls) {
        log(LOG_LEVEL.DEBUG, `从live_core_sdk_data中提取到蓝光HLS链接`);
        const hlsUrl = streamData.data.origin.main.hls;
        backupUrls.push({url: hlsUrl, quality: 'origin', type: 'hls'});
      }
      // 可以考虑再提取一个明确的次高画质HLS，如md，以防origin在某些情况下解析不正确或不可用
      if (streamData.data && streamData.data.md && streamData.data.md.main && 
          streamData.data.md.main.hls) {
        log(LOG_LEVEL.DEBUG, `从live_core_sdk_data中提取到md画质HLS链接作为备用`);
        const hlsUrl = streamData.data.md.main.hls;
        backupUrls.push({url: hlsUrl, quality: 'md', type: 'hls'});
      }      
      
      if (backupUrls.length > 0) {
        backupUrls.forEach((item, index) => {
          log(LOG_LEVEL.DEBUG, `[extractBestStreamUrl] 备用HLS链接 ${index+1}: 画质=${item.quality}, URL=${item.url.substring(0, 100)}...`);
        });
        streamObj._backup_urls = backupUrls; // 存储所有提取到的HLS备用链接
        return backupUrls[0].url; // 返回最高优先级的HLS链接
      }
    } catch (err) {
      log(LOG_LEVEL.ERROR, `解析stream_data失败: ${err.message}`);
    }
    // 如果live_core_sdk_data解析失败或无HLS，尝试从顶层结构获取
  }
  
  // 清晰度优先级（仅HLS相关）：FULL_HD1 > HD1 > SD1 > SD2
  const qualityPriority = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
  const douyinQualityMap = {
    'origin': 'FULL_HD1', 'hd': 'HD1', 'sd': 'SD1', 'ld': 'SD2'
  };
  
  let bestHlsUrl = null;

  // 优先处理抖音平台的hls_pull_url_map
  if (platform === 'douyin' && streamObj.hls_pull_url_map) {
    const douyinPriority = ['origin', 'hd', 'sd', 'ld'];
    for (const qKey of douyinPriority) {
      if (streamObj.hls_pull_url_map[qKey]) {
        const standardQuality = douyinQualityMap[qKey] || 'FULL_HD1';
        log(LOG_LEVEL.DEBUG, `抖音平台使用最高可用HLS清晰度: ${qKey} (映射为: ${standardQuality})`);
        return streamObj.hls_pull_url_map[qKey];
      }
    }
  }
  
  // 标准HLS URL提取 (hls_pull_url_map)
  if (streamObj.hls_pull_url_map) {
    for (const q of qualityPriority) {
      if (streamObj.hls_pull_url_map[q]) {
        log(LOG_LEVEL.DEBUG, `选择HLS清晰度: ${q}`);
        return streamObj.hls_pull_url_map[q];
      }
    }
  }
  
  // 尝试直接使用顶层的hls_pull_url (如果存在且是HLS)
  if (streamObj.hls_pull_url && (streamObj.hls_pull_url.includes('.m3u8') || streamObj.hls_pull_url.includes('/hls/'))) {
    log(LOG_LEVEL.DEBUG, `使用默认HLS流: ${streamObj.hls_pull_url}`);
    return streamObj.hls_pull_url;
  }
  
  // 如果前面所有尝试都失败了，检查之前在live_core_sdk_data中是否存有备用链接
  if (streamObj._backup_urls && streamObj._backup_urls.length > 0) {
    log(LOG_LEVEL.DEBUG, '无直接HLS链接，使用从live_core_sdk_data提取的备用HLS链接');
    return streamObj._backup_urls[0].url; 
  }

  log(LOG_LEVEL.WARN, `无法从流对象中提取任何有效的HLS URL: ${JSON.stringify(streamObj).substring(0,200)}`);
  return null;
}

/**
 * 获取流对象中指定清晰度索引的链接
 * @param {object} streamObj 流对象
 * @param {number} qualityIndex 清晰度索引，0为最高清晰度，越大清晰度越低
 * @returns {string|null} 链接或null
 */
function getUrlByQualityIndex(streamObj, qualityIndex) {
  if (!streamObj) return null;
  
  const qualityPriority = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
  
  // 如果索引超出范围，返回null
  if (qualityIndex < 0 || qualityIndex >= qualityPriority.length) return null;
  
  // 优先尝试HLS
  if (streamObj.hls_pull_url_map && streamObj.hls_pull_url_map[qualityPriority[qualityIndex]]) {
    return streamObj.hls_pull_url_map[qualityPriority[qualityIndex]];
  }
  
  // 其次尝试FLV
  if (streamObj.flv_pull_url && typeof streamObj.flv_pull_url === 'object' && 
      streamObj.flv_pull_url[qualityPriority[qualityIndex]]) {
    return streamObj.flv_pull_url[qualityPriority[qualityIndex]];
  }
  
  // 如果指定清晰度没有，返回null
  return null;
}

/**
 * 验证HLS流的可用性
 * @param {string} url HLS流地址
 * @returns {Promise<boolean>} 是否可用
 */
async function validateHlsStream(url, anchorId = '') { // 添加 anchorId 以便日志关联
  try {
    const isDouyinUrl = url.includes('douyincdn.com') || 
                        url.includes('douyinliving.com') || 
                        url.includes('douyinvod.com') ||
                        url.includes('flive.douyincdn.com'); // 增加了 flive.douyincdn.com
    
    if (isDouyinUrl) {
      const expireMatch = url.match(/expire=(\d+)/);
      if (expireMatch) {
        const expireTimestamp = parseInt(expireMatch[1], 10);
        const currentTimestamp = Math.floor(Date.now() / 1000);
        if (expireTimestamp <= currentTimestamp + 300) { // 提前5分钟判断为即将过期
          log(LOG_LEVEL.WARN, `HLS流已过期或即将过期 (expire=${expireTimestamp}, current=${currentTimestamp})，判定无效: ${url}`, anchorId);
          return false;
        }
      }
    }
  
    const cmd = `ffprobe -v error -probesize 20M -analyzeduration 15M -show_entries stream=codec_type -select_streams v:0 -of json -i "${url}" -timeout 30000000`; // 降低探测大小和时间，增加超时
    const { stdout, stderr } = await execPromise(cmd, { timeout: 45000 }); // 给ffprobe本身也设置一个执行超时
    
    if (stderr) {
        // FFprobe的stderr输出通常包含了错误的关键信息
        if (stderr.includes('404 Not Found') || stderr.includes('Server returned 404 Not Found')) {
            log(LOG_LEVEL.WARN, `HLS流返回404错误 (ffprobe stderr)，判定无效: ${url}`, anchorId);
            return false;
        }
        if (stderr.includes('403 Forbidden') || stderr.includes('Server returned 403 Forbidden')) {
            log(LOG_LEVEL.WARN, `HLS流返回403禁止访问 (ffprobe stderr)，判定无效: ${url}`, anchorId);
            return false;
        }
        // 其他stderr内容也可能指示问题，但暂时不直接判false，除非下面JSON解析也失败
        log(LOG_LEVEL.DEBUG, `HLS验证ffprobe stderr: ${stderr.substring(0,100)}`, anchorId)
    }

    try {
      const result = JSON.parse(stdout);
      if (result.streams && result.streams.length > 0 && result.streams.some(stream => stream.codec_type === 'video')) {
        log(LOG_LEVEL.INFO, `HLS流验证成功: ${url}`, anchorId);
        return true;
      }
      log(LOG_LEVEL.WARN, `HLS流没有找到视频流 (JSON解析成功但无视频): ${url}`, anchorId);
      return false;
    } catch (parseErr) {
      if (stdout && stdout.includes('Video')) { // 作为最后的补救措施
        log(LOG_LEVEL.INFO, `HLS流验证成功 (非JSON响应但包含Video关键词): ${url}`, anchorId);
        return true;
      }
      log(LOG_LEVEL.ERROR, `HLS流验证JSON解析失败 (${parseErr.message})，stdout: ${stdout.substring(0,100)}，判定无效: ${url}`, anchorId);
      return false;
    }
  } catch (err) {
    // 这个catch块捕获 execPromise 的直接错误 (例如超时，或ffprobe命令不存在等)
    if (err.message.includes('404 Not Found') || err.message.includes('Server returned 404 Not Found')) {
      log(LOG_LEVEL.WARN, `HLS流返回404错误 (execPromise catch)，判定无效: ${url}`, anchorId);
    } else if (err.message.includes('403 Forbidden') || err.message.includes('Server returned 403 Forbidden')) {
      log(LOG_LEVEL.WARN, `HLS流返回403禁止访问 (execPromise catch)，判定无效: ${url}`, anchorId);
    } else if (err.timedOut) {
      log(LOG_LEVEL.WARN, `HLS流验证ffprobe执行超时，判定无效: ${url}`, anchorId);
    } else {
      log(LOG_LEVEL.ERROR, `HLS流验证执行失败 (execPromise catch): ${err.message.substring(0,100)}... URL: ${url}`, anchorId);
    }
    return false;
  }
}

/**
 * 从HLS流中检测最佳分辨率
 * @param {string} url HLS流地址
 * @returns {Promise<string|null>} 最佳分辨率或null
 */
async function detectBestHlsResolution(url) {
  try {
    const cmd = `ffprobe -v error -show_entries stream=width,height -of json -i "${url}"`;
    const { stdout } = await execPromise(cmd);
    const result = JSON.parse(stdout);
    
    let maxWidth = 0;
    let bestResolution = null;
    
    if (result.streams) {
      for (const stream of result.streams) {
        if (stream.width && stream.width > maxWidth) {
          maxWidth = stream.width;
          bestResolution = `${stream.width}x${stream.height}`;
        }
      }
    }
    
    if (bestResolution) {
      console.log(`[${getCurrentTime()}] 检测到HLS流最佳分辨率: ${bestResolution}, URL: ${url}`);
      return bestResolution;
    }
    
    return null;
  } catch (err) {
    console.error(`[${getCurrentTime()}] 检测HLS分辨率失败: ${err.message}, URL=${url}`);
    return null;
  }
}

/**
 * 从数据库获取主播最新的流链接
 * @param {string} anchorId 主播ID
 * @returns {Promise<object|string|null>} 流链接对象或null
 */
async function getLatestStreamUrl(anchorId) {
  try {
    return await withRetry(async () => {
      const { data, error } = await supabase
        .from('anchors')
        .select('stream_url, anchor_name, new_url')
        .eq('anchor_id', anchorId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (!data || !data.stream_url) {
        log(LOG_LEVEL.WARN, `未找到主播流链接`, anchorId);
        return null;
      }

      let streamUrl = data.stream_url;
      const anchorName = data.anchor_name || '';
      const newUrl = data.new_url || '';
      
      // 解析stream_url
      try {
        let parsed;
        if (typeof streamUrl === 'string') {
          if (streamUrl.startsWith('http')) {
            parsed = streamUrl;
          } else {
            parsed = JSON.parse(streamUrl);
          }
        } else {
          parsed = streamUrl;
        }
        
        // 提取最佳URL用于去重比较
        const bestUrl = typeof parsed === 'string' ? parsed : extractBestStreamUrl(parsed);
        
        // 检查是否与上次URL相同
        if (lastUsedUrls.has(anchorId) && lastUsedUrls.get(anchorId) === bestUrl) {
          log(LOG_LEVEL.WARN, `获取到的链接与上次相同，可能需要等待平台更新 (anchor_name: ${anchorName})`, anchorId);
          // 如果连续获取到相同URL，等待更长时间避免频繁重试
          await new Promise(r => setTimeout(r, 10000));
        } else if (bestUrl) {
          // 记录新的URL
          lastUsedUrls.set(anchorId, bestUrl);
        }
        
        // 将new_url添加到返回结果中
        if (typeof parsed === 'object') {
          parsed._new_url = newUrl;
          parsed._anchor_name = anchorName;
        }
        
        return parsed;
      } catch (e) {
        throw new Error(`解析流链接失败: ${e.message}`);
      }
    }, 3, '获取流链接', anchorId);
  } catch (err) {
    logError(`获取最新流链接时发生异常`, err, anchorId);
    return null;
  }
}

/**
 * 从流对象中提取特定画质的URL
 * @param {object} streamObj 流对象
 * @param {string} qualityLevel 需要的画质级别
 * @param {boolean} preferHls 是否优先HLS
 * @returns {string|null} URL或null
 */
function extractSpecificQualityUrl(streamObj, qualityLevel, preferHls = true) {
  if (!streamObj || !qualityLevel) return null;
  
  // 首先尝试HLS
  if (preferHls && streamObj.hls_pull_url_map && streamObj.hls_pull_url_map[qualityLevel]) {
    return streamObj.hls_pull_url_map[qualityLevel];
  }
  
  // 其次尝试FLV
  if (streamObj.flv_pull_url && streamObj.flv_pull_url[qualityLevel]) {
    return streamObj.flv_pull_url[qualityLevel];
  }
  
  // 最后尝试默认HLS
  if (preferHls && streamObj.hls_pull_url) {
    return streamObj.hls_pull_url;
  }
  
  return null;
}

/**
 * 从URL中提取画质等级
 * @param {string} url 流URL
 * @returns {string|null} 画质等级或null
 */
function extractQualityFromUrl(url) {
  if (!url) return null;
  
  // 检查URL是否包含明确的画质标识
  // 例如: stream-xxx_uhd.m3u8, stream-xxx_hd.m3u8, stream-xxx_sd.m3u8, stream-xxx_ld.m3u8
  const qualityMap = {
    '_uhd.': 'FULL_HD1',
    '_hd.': 'HD1',
    '_ld.': 'SD1', 
    '_sd.': 'SD2',
    // 抖音平台特有命名
    '_origin.': 'FULL_HD1',
    '_or4.': 'FULL_HD1',   // 蓝光原画
    '_720p.': 'HD1'         // 720p
  };
  
  for (const [suffix, quality] of Object.entries(qualityMap)) {
    if (url.includes(suffix)) {
      return quality;
    }
  }
  
  // 通过链接结构推断
  if (url.includes('FULL_HD1') || url.includes('uhd') || url.includes('origin') || url.includes('or4')) {
    return 'FULL_HD1';
  } else if (url.includes('HD1') || url.includes('hd') || url.includes('720p')) {
    return 'HD1';
  } else if (url.includes('SD1') || url.includes('ld')) {
    return 'SD1';
  } else if (url.includes('SD2') || url.includes('sd')) {
    return 'SD2';
  }
  
  return null;
}

// 存储解析后的抖音配置
let douyinConfig = null;
let lastConfigLoadTime = 0;
const CONFIG_RELOAD_INTERVAL = 5 * 60 * 1000; // 5分钟重新加载一次配置

/**
 * 读取并解析抖音配置文件
 * @returns {Promise<Object|null>} 解析后的配置对象或null
 */
async function loadDouyinConfig() {
  try {
    const configPath = '/home/<USER>/API/Douyin_TikTok_Download_API/crawlers/douyin/web/config.yaml';
    const currentTime = Date.now();
    
    // 如果配置已加载且未超过重新加载间隔，直接返回缓存的配置
    if (douyinConfig && (currentTime - lastConfigLoadTime < CONFIG_RELOAD_INTERVAL)) {
      return douyinConfig;
    }
    
    // 检查文件是否存在
    await fs.promises.access(configPath, fs.constants.R_OK);
    
    // 读取并解析YAML
    const configContent = await fs.promises.readFile(configPath, 'utf8');
    const parsedConfig = yaml.load(configContent);
    
    // 更新全局配置和加载时间
    douyinConfig = parsedConfig;
    lastConfigLoadTime = currentTime;
    
    log(LOG_LEVEL.INFO, `成功加载抖音配置文件，时间: ${new Date().toISOString()}`);
    return parsedConfig;
  } catch (err) {
    logError(`加载抖音配置文件失败`, err);
    return null;
  }
}

/**
 * 为抖音链接构建HTTP请求头
 * @returns {Promise<string|null>} 格式化的HTTP头部字符串或null
 */
async function buildDouyinHttpHeaders() {
  try {
    const config = await loadDouyinConfig();
    if (!config || !config.TokenManager || !config.TokenManager.douyin || !config.TokenManager.douyin.headers) {
      return null;
    }
    
    const headers = config.TokenManager.douyin.headers;
    let headerStr = '';
    
    // 构建HTTP头部字符串，每个头部之间用\r\n分隔
    for (const [key, value] of Object.entries(headers)) {
      // 不再通过此函数添加 Cookie Header，yt-dlp 有更好的处理方式
      if (key.toLowerCase() === 'cookie') {
        log(LOG_LEVEL.DEBUG, "[buildDouyinHttpHeaders] Skipping Cookie header, will be handled by yt-dlp directly.");
        continue;
      }
      headerStr += `${key}: ${value}\r\n`;
    }
    
    return headerStr;
  } catch (err) {
    logError(`构建抖音HTTP头部失败`, err);
    return null;
  }
}

/**
 * 辅助函数：尝试发送最后一个文件段到 Redis
 * @param {string} anchorId 主播ID
 * @param {string} filePath 文件路径
 * @param {string} taskStartTime 整个录制任务的开始时间
 */
async function trySendLastSegment(anchorId, filePath, taskStartTime) {
  if (!filePath) {
    log(LOG_LEVEL.DEBUG, `[trySendLastSegment] 文件路径为空，跳过发送 (anchor_id: ${anchorId})`);
    return;
  }
  try {
    const stat = await fs.promises.stat(filePath);
    if (stat.size > 0) {
      await redis.xadd(
        REDIS_POST_STREAM,
        '*',
        'anchor_id', anchorId,
        'file_path', filePath,
        'recordtime', taskStartTime,
        'filesize', stat.size.toString()
      );
      log(LOG_LEVEL.INFO, `[trySendLastSegment] 成功补发文件信息到 Redis: ${filePath}, anchor_id: ${anchorId}`);
    } else {
      log(LOG_LEVEL.WARN, `[trySendLastSegment] 文件为空，不发送: ${filePath}, anchor_id: ${anchorId}`);
    }
  } catch (err) {
    if (err.code === 'ENOENT') {
      log(LOG_LEVEL.INFO, `[trySendLastSegment] 文件未找到，静默失败: ${filePath}, anchor_id: ${anchorId}`);
    } else {
      logError(`[trySendLastSegment] 发送文件 ${filePath} 到 Redis 时出错`, err, anchorId);
    }
  }
}

/**
 * ------------------------------  基于 ffmpeg 的录制实现  ------------------------------
 * 1. 使用 ffmpeg 把 HLS/FLV 直接写到 stdout，然后由 Node 追加写入同一个 TS 文件；
 * 2. 文件达到 FILE_SIZE_LIMIT_BYTES 时滚动新文件；
 * 3. 当 ffmpeg 因网络/链接过期退出时，自动获取最新 URL 并续写到同一个文件；
 * 4. 返回值仍保持 { ytdlpProcess, recordingPromise, isStopping } 字段，便于与旧逻辑兼容。
 */

function spawnFfmpegProcess(streamUrl, extraHeaders = []) {
  const ffArgs = [
    '-loglevel', 'error',
    '-reconnect', '1',
    '-reconnect_streamed', '1',
    '-reconnect_delay_max', '5',
    '-i', streamUrl,
    '-c', 'copy',
    '-f', 'mpegts',
    'pipe:1'
  ];

  // 追加自定义 HTTP 头
  if (extraHeaders && extraHeaders.length) {
    extraHeaders.forEach(h => {
      ffArgs.unshift('-headers', h);
    });
  }

  return spawn('ffmpeg', ffArgs, { stdio: ['ignore', 'pipe', 'inherit'] });
}

async function recordStreamFfmpeg(streamUrlObject, anchorId, anchorInfo) {
  let currentUrl = extractBestStreamUrl(streamUrlObject, null, anchorInfo.platform);
  if (!currentUrl) throw new Error('无法从流对象中提取有效URL');

  const subdir = path.join(RECORDING_DIR, anchorId);
  await fs.promises.mkdir(subdir, { recursive: true });

  const recordTime = new Date().toISOString();
  let currentFilePath = path.join(subdir, `${Math.floor(Date.now() / 1000)}-${anchorId}.ts`);

  let writer = fs.createWriteStream(currentFilePath, { flags: 'a' });

  // 初始化 activeRecordings 信息
  if (activeRecordings.has(anchorId)) {
    const rec = activeRecordings.get(anchorId);
    rec.latestOutputFile = currentFilePath;
    rec.taskRecordTime = recordTime;
  }

  await updateAnchorStatus(anchorId, { is_recording: true, finishtime: null });
  await insertRecordTime(anchorId, recordTime);

  let isStopping = false;
  let ffmpegProcess = null;

  // 构建适用于当前 URL 的 HTTP 头
  async function buildHeadersForUrl(url) {
    const headers = [];
    const isDouyinUrl = url.includes('douyincdn.com') || url.includes('douyinliving.com') || url.includes('douyinvod.com');
    if (isDouyinUrl && (anchorInfo.platform === 'douyin' || anchorInfo.platform === 'tiktok')) {
      const douyinHeaderStr = await buildDouyinHttpHeaders();
      if (douyinHeaderStr) {
        headers.push(...douyinHeaderStr.trim().split('\r\n').filter(Boolean));
      } else {
        headers.push('User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'Referer: https://live.douyin.com/');
      }
    }
    return headers;
  }

  async function launchFfmpeg(url) {
    // 若 HLS 验证失败，先不启动
    const isOk = await validateHlsStream(url, anchorId);
    if (!isOk) return null;
    const headersArr = await buildHeadersForUrl(url);
    const proc = spawnFfmpegProcess(url, headersArr);
    proc.stdout.pipe(writer, { end: false });
    return proc;
  }

  ffmpegProcess = await launchFfmpeg(currentUrl);
  if (ffmpegProcess) {
    ffmpegProcess.once('close', onInitialClose);
  } else {
    log(LOG_LEVEL.WARN, '初次 URL 验证失败，将在 10s 后重试', anchorId);
    setTimeout(async () => {
      if (!isStopping) restartInitial();
    }, 10000);
  }

  // 初次失败后的重试逻辑
  async function restartInitial() {
    currentUrl = (await getLatestStreamUrl(anchorId)) || currentUrl;
    ffmpegProcess = await launchFfmpeg(currentUrl);
    if (ffmpegProcess) {
      ffmpegProcess.once('close', onInitialClose);
    } else if (!isStopping) {
      setTimeout(restartInitial, 10000);
    }
  }

  function onInitialClose(code, signal) {
    // 将控制权交给 recordingPromise 内部逻辑
  }

  // 周期检测文件大小，超限滚动
  const sizeChecker = setInterval(async () => {
    try {
      const stat = await fs.promises.stat(currentFilePath);
      if (stat.size >= FILE_SIZE_LIMIT_BYTES) {
        // 关闭旧 writer 并发送信息到 Redis
        writer.end();
        await redis.xadd(
          REDIS_POST_STREAM,
          '*',
          'anchor_id', anchorId,
          'file_path', currentFilePath,
          'recordtime', recordTime,
          'filesize', stat.size.toString()
        );

        // 打开新文件
        currentFilePath = path.join(subdir, `${Math.floor(Date.now() / 1000)}-${anchorId}.ts`);
        writer = fs.createWriteStream(currentFilePath, { flags: 'a' });
        if (activeRecordings.has(anchorId)) {
          const rec = activeRecordings.get(anchorId);
          rec.latestOutputFile = currentFilePath;
        }

        // 重启 ffmpeg 到同一 URL（文件已变更，继续写入）
        if (ffmpegProcess && !ffmpegProcess.killed) ffmpegProcess.kill('SIGINT');
        ffmpegProcess = await launchFfmpeg(currentUrl);
        if (!ffmpegProcess) return; // 如果仍拿不到有效链接，等待下一轮 onClose 触发再次尝试
      }
    } catch (e) {
      log(LOG_LEVEL.WARN, `大小检测失败: ${e.message}`, anchorId);
    }
  }, 15000);

  const recordingPromise = new Promise((resolve, reject) => {
    const restart = async () => {
      try {
        // 获取最新 URL
        const latestObj = await getLatestStreamUrl(anchorId);
        const newUrl = latestObj ? extractBestStreamUrl(latestObj, null, anchorInfo.platform) : null;
        if (newUrl) currentUrl = newUrl;
      } catch {}

      if (!isStopping) {
        ffmpegProcess = await launchFfmpeg(currentUrl);
        ffmpegProcess.once('close', onClose);
      }
    };

    const onClose = (code, signal) => {
      if (isStopping) {
        clearInterval(sizeChecker);
        writer.end();
        cleanupDone.set(anchorId, true);
        resolve();
        return;
      }
      log(LOG_LEVEL.WARN, `ffmpeg 退出(code=${code}, signal=${signal})，尝试重启`, anchorId);
      restart();
    };

    ffmpegProcess.once('close', onClose);
  });

  return { ytdlpProcess: ffmpegProcess, recordingPromise, isStopping };
}

/**
 * 检查并重新启动遗漏录制
 */
async function checkAndRestartMissedRecordings() {
  log(LOG_LEVEL.INFO, `开始检查并重新启动遗漏的录制条目...`);
  try {
    // 不再限制为 record = true 的条目
    const { data: anchorsInRange, error } = await supabase
      .from('anchors')
      .select('anchor_id, stream_url, finishtime, ordertime, status, is_recording, record, timestamp, anchor_name, platform')
      .order('timestamp', { ascending: true })
      .range(currentOffset, currentOffset + currentLimit - 1);

    if (error) {
      log(LOG_LEVEL.ERROR, `获取当前区间内的anchors时出错: ${error.message}`);
      return;
    }

    // 添加更详细的统计信息
    log(LOG_LEVEL.INFO, `在区间 [${currentOffset},${currentOffset + currentLimit - 1}] 内共找到 ${anchorsInRange ? anchorsInRange.length : 0} 条记录`);
    
    if (!anchorsInRange || anchorsInRange.length === 0) {
      log(LOG_LEVEL.INFO, `没有找到记录，请检查数据库配置`);
      return;
    }
    
    // 增加详细统计
    let statusTrueCount = 0;
    let notRecordingCount = 0;
    let needRecordCount = 0;
    let hasStreamUrlCount = 0;
    let noStreamUrlCount = 0;
    let isRecordingCount = 0;
    
    // 检查哪些主播已经在录制中
    const recordingAnchors = [];
    const statusTrueNotRecordingAnchors = [];
    
    const currentTime = new Date();
    let resendCount = 0;
    
    // 存储status=false但仍在录制的异常主播
    const abnormalAnchors = [];
    
    for (const recording of anchorsInRange) {
      // 把已经在录制中的记录一下
      if (recording.is_recording === true) {
        isRecordingCount++;
        recordingAnchors.push({
          anchor_id: recording.anchor_id,
          anchor_name: recording.anchor_name,
          platform: recording.platform,
          timestamp: recording.timestamp
        });
        
        // 检测异常情况：status=false但is_recording=true
        if (recording.status === false) {
          abnormalAnchors.push({
            anchor_id: recording.anchor_id,
            anchor_name: recording.anchor_name,
            platform: recording.platform
          });
        }
      }
      
      // 统计 status=true 的记录
      if (recording.status === true) {
        statusTrueCount++;
        
        // 统计未在录制中的记录
        if (recording.is_recording === false) {
          notRecordingCount++;
          
          statusTrueNotRecordingAnchors.push({
            anchor_id: recording.anchor_id,
            anchor_name: recording.anchor_name,
            platform: recording.platform,
            finishtime: recording.finishtime,
            ordertime: recording.ordertime,
            stream_url: !!recording.stream_url
          });
          
          let shouldResend = false;
          let reason = '';

          // 检查是否有stream_url，没有的话记录一下
          if (!recording.stream_url) {
            noStreamUrlCount++;
            log(LOG_LEVEL.DEBUG, `主播 ${recording.anchor_id} (${recording.anchor_name}) 缺少stream_url，跳过`, recording.anchor_id);
            continue;
          } else {
            hasStreamUrlCount++;
          }

          // 修改为无条件录制，不再考虑finishtime或ordertime
          shouldResend = true;
          reason = `status=true且is_recording=false，触发检查立即录制`;
          needRecordCount++;

          if (shouldResend) {
            log(LOG_LEVEL.INFO, 
              `重新开始录制主播 ${recording.anchor_id}, anchor_name=${recording.anchor_name}, platform=${recording.platform}，原因: ${reason}`);
            startRecording(recording.anchor_id, recording.stream_url);
            await incrementCount(recording.anchor_id, 'bcount');
            resendCount++;
          }
        } else {
          log(LOG_LEVEL.DEBUG, `主播 ${recording.anchor_id} (${recording.anchor_name}) 已经在录制中，跳过`, recording.anchor_id);
        }
      }
    }
    
    // 处理异常录制情况：status=false但is_recording=true
    if (abnormalAnchors.length > 0) {
      log(LOG_LEVEL.WARN, `检测到 ${abnormalAnchors.length} 个异常录制情况：status=false但is_recording=true`);
      for (const anchor of abnormalAnchors) {
        const anchorId = anchor.anchor_id;
        
        // 更新计数器
        const currentCount = abnormalRecordingCounts.get(anchorId) || 0;
        abnormalRecordingCounts.set(anchorId, currentCount + 1);
        
        log(LOG_LEVEL.WARN, `主播 ${anchorId} (${anchor.anchor_name}) 已下播但录制未停止，连续检测次数: ${currentCount + 1}/3`);
        
        // 如果连续3次检测到，强制终止录制
        if (currentCount + 1 >= 3) {
          log(LOG_LEVEL.ERROR, `主播 ${anchorId} (${anchor.anchor_name}) 连续3次检测到异常，强制终止录制`);
          
          if (activeRecordings.has(anchorId)) {
            const { ytdlpProcess } = activeRecordings.get(anchorId);
            if (ytdlpProcess && !ytdlpProcess.killed) {
              ytdlpProcess.kill('SIGINT');
              log(LOG_LEVEL.INFO, `已发送SIGINT信号终止主播 ${anchorId} 的录制进程`);
            }
          }
          
          // 更新数据库状态
          await updateAnchorStatus(anchorId, { is_recording: false, finishtime: new Date().toISOString() });
          abnormalRecordingCounts.delete(anchorId); // 重置计数
        }
      }
    }
    
    // 清理不再异常的主播计数
    for (const [anchorId, count] of abnormalRecordingCounts.entries()) {
      if (!abnormalAnchors.some(a => a.anchor_id === anchorId)) {
        abnormalRecordingCounts.delete(anchorId);
        log(LOG_LEVEL.INFO, `主播 ${anchorId} 不再处于异常录制状态，已清除计数`);
      }
    }
    
    // 不再打印录制中的主播列表，避免日志过多
    
    // 输出本机实际正在录制的主播列表
    if (activeRecordings.size > 0) {
      log(LOG_LEVEL.INFO, `本机实际正在录制的主播列表(${activeRecordings.size}个)：`);
      let idx = 1;
      for (const [anchorId, { anchorInfo }] of activeRecordings.entries()) {
        log(LOG_LEVEL.INFO, `${idx++}. anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}`);
      }
    } else {
      log(LOG_LEVEL.INFO, `本机当前没有正在录制的主播`);
    }
    
    // 不再打印status=true但未录制的主播列表，可以直接从数据库查询

    // 输出统计信息摘要
    log(LOG_LEVEL.INFO, `检查完毕，状态摘要: 
    - status=true: ${statusTrueCount}/${anchorsInRange.length}条
    - 正在录制 (is_recording=true): ${isRecordingCount}/${anchorsInRange.length}条
    - 未录制 (is_recording=false): ${notRecordingCount}/${statusTrueCount}条
    - 有流地址: ${hasStreamUrlCount}/${notRecordingCount}条
    - 无流地址: ${noStreamUrlCount}/${notRecordingCount}条
    - 需要录制: ${needRecordCount}/${notRecordingCount}条
    - 成功开始: ${resendCount}/${needRecordCount}条
    - 异常录制: ${abnormalAnchors.length}条`);
    
  } catch (err) {
    logError(`检查并重新启动遗漏的录制时发生异常`, err);
  }
}

/**
 * 优雅关闭逻辑
 */
const gracefulShutdown = async () => {
  console.log(`\n[${getCurrentTime()}] 接收到退出信号，正在关闭录制任务...`);
  shuttingDown = true; // 设置全局关闭标志

  const stopPromises = [];
  const anchorIdsToUpdate = Array.from(activeRecordings.keys());

  // 第一阶段：设置所有录制任务的isStopping标志，并立即尝试SIGINT
  for (const [anchorId, recordingInfo] of activeRecordings.entries()) { // Changed variable name from recording to recordingInfo
    const anchorInfo = recordingInfo.anchorInfo || { anchor_name: '未知', platform: '未知' };
    console.log(
      `[${getCurrentTime()}] 优雅终止主播 ${anchorId} (anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}) 的录制任务...`
    );
    
    // 直接修改activeRecordings中的isStopping标志
    recordingInfo.isStopping = true;
    
    // 立即尝试发送 SIGINT
    const { ytdlpProcess } = recordingInfo;
    if (ytdlpProcess && !ytdlpProcess.killed) {
      log(LOG_LEVEL.INFO, `[Graceful Shutdown] 发送 SIGINT 到 yt-dlp 进程 for anchor_id: ${anchorId}`);
      ytdlpProcess.kill('SIGINT');
    }
    
    if (recordingInfo.recordingPromise) {
      // 创建一个Promise，它会等待录制任务正常完成，或在超时后强制终止
      stopPromises.push(
        Promise.race([
          // 等待录制任务正常完成
          recordingInfo.recordingPromise.catch(err => { // Changed variable name
            log(LOG_LEVEL.WARN, `录制任务结束时发生错误: ${err.message}`, anchorId);
          }),
          
          // 设置超时，如果SIGINT后等待太久，强制终止进程
          new Promise(resolve => {
            setTimeout(async () => { // Make this async to use await inside
              log(LOG_LEVEL.WARN, `等待录制任务(SIGINT后)正常结束超时，将强制终止 SIGKILL`, anchorId);
              // 再次检查 activeRecordings 是否还有该条目，以及进程是否仍在运行
              if (activeRecordings.has(anchorId)) {
                const currentRec = activeRecordings.get(anchorId);
                if (currentRec && currentRec.ytdlpProcess && !currentRec.ytdlpProcess.killed) {
                  log(LOG_LEVEL.WARN, `yt-dlp进程未响应SIGINT，发送SIGKILL强制终止`, anchorId);
                  currentRec.ytdlpProcess.kill('SIGKILL');
                }

                // 检查是否需要补发 Redis 消息
                if (!cleanupDone.has(anchorId) && currentRec.latestOutputFile && currentRec.taskRecordTime) {
                  log(LOG_LEVEL.INFO, `[Graceful Shutdown Timeout] cleanupDone 未标记 for ${anchorId}，尝试补发最后一个文件段: ${currentRec.latestOutputFile}`);
                  await trySendLastSegment(anchorId, currentRec.latestOutputFile, currentRec.taskRecordTime);
                } else if (cleanupDone.has(anchorId)){
                  log(LOG_LEVEL.INFO, `[Graceful Shutdown Timeout] cleanupDone 已标记 for ${anchorId}，无需补发。`);
                } else {
                  log(LOG_LEVEL.WARN, `[Graceful Shutdown Timeout] 无法补发 for ${anchorId}: latestOutputFile=${currentRec.latestOutputFile}, taskRecordTime=${currentRec.taskRecordTime}`);
                }
              }
              resolve();
            }, 10000); // 例如，给10秒让SIGINT生效并清理，否则SIGKILL
          })
        ])
      );
    }
  }

  log(LOG_LEVEL.INFO, "等待所有录制任务完成或超时...");
  await Promise.allSettled(stopPromises); // 等待所有录制任务结束或超时
  
  log(LOG_LEVEL.INFO, "所有录制进程已停止，更新数据库状态并确认文件已发送到Redis...");

  // 最后一次检查，确保所有活跃录制的最后文件段都已发送到Redis
  for (const anchorId of anchorIdsToUpdate) {
    if (activeRecordings.has(anchorId)) {
      const currentRec = activeRecordings.get(anchorId);
      
      // 再次尝试补发文件，以防前面的尝试失败
      if (!cleanupDone.has(anchorId) && currentRec.latestOutputFile && currentRec.taskRecordTime) {
        log(LOG_LEVEL.INFO, `[Graceful Shutdown Final Check] 最终检查：尝试补发文件段 for ${anchorId}: ${currentRec.latestOutputFile}`);
        await trySendLastSegment(anchorId, currentRec.latestOutputFile, currentRec.taskRecordTime);
      }
      
      log(LOG_LEVEL.INFO, `[Graceful Shutdown] 更新主播 ${anchorId} 状态为未录制`);
      await updateAnchorStatus(anchorId, { is_recording: false, finishtime: new Date().toISOString() });
      activeRecordings.delete(anchorId); // 从activeRecordings中移除
    }
  }
  
  activeRecordings.clear(); // 确保清空

  // 关闭Redis和Supabase连接
  if (redis) {
    try {
      await redis.quit();
      log(LOG_LEVEL.INFO, "Redis 连接已关闭。");
    } catch (e) {
      log(LOG_LEVEL.WARN, `关闭 Redis 连接时出错: ${e.message}`);
    }
  }
  if (supabase && typeof supabase.removeAllChannels === 'function') { // 检查supabase对象和方法是否存在
    try {
      await supabase.removeAllChannels();
      log(LOG_LEVEL.INFO, "Supabase channels 已移除。");
    } catch (e) {
      log(LOG_LEVEL.WARN, `移除 Supabase channels 时出错: ${e.message}`);
    }
  }

  console.log(`[${getCurrentTime()}] 程序已安全退出`);
  process.exit(0);
};

process.on('SIGINT', gracefulShutdown);

/**
 * 监听 does 表的变化
 */
supabase
  .channel('does-changes')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'does',
    },
    onDoesChange
  )
  .subscribe();

/**
 * 当 anchors 表发生变化时的回调函数
 */
const onAnchorChange = async (payload) => {
  log(LOG_LEVEL.DEBUG, `[onAnchorChange] anchors 表发生变化: ${JSON.stringify(payload)}`);
  const newRecord = payload.new;
  const oldRecord = payload.old;

  if (payload.eventType === 'UPDATE' && 
      newRecord && 
      oldRecord && 
      oldRecord.status === false && 
      newRecord.status === true && 
      newRecord.is_recording === false) {
    
    const anchorId = newRecord.anchor_id;
    const anchorName = newRecord.anchor_name || '未知';
    const platform = newRecord.platform || '未知';

    log(LOG_LEVEL.INFO, `[onAnchorChange] 检测到主播 ${anchorId} (${anchorName}) 开播 (status: false->true, is_recording: false)`);

    // 1. 检查该 anchor_id 是否在当前实例的负责范围内
    // 这是关键一步，以避免多个实例同时录制
    // 我们需要一种方法来确定 anchor_id 的全局顺序或索引
    // 假设 anchors 表有一个 id 字段作为自增主键，可以用来判断范围
    // 如果没有，则需要一种方法获取 anchor_id 相对于整个表的行号或排序位置
    try {
      const { data: anchorDetails, error: detailsError } = await supabase
        .from('anchors')
        .select('id') // 假设有一个名为 'id' 的自增主键列用于排序和范围判断
                       // 或者您可以选择一个能代表顺序的列，比如 timestamp (如果可靠)
        .eq('anchor_id', anchorId)
        .single();

      if (detailsError) {
        log(LOG_LEVEL.ERROR, `[onAnchorChange] 查询主播 ${anchorId} 的详情失败: ${detailsError.message}`);
        return;
      }

      if (!anchorDetails || typeof anchorDetails.id === 'undefined') {
        log(LOG_LEVEL.ERROR, `[onAnchorChange] 未能获取主播 ${anchorId} 的顺序标识 (id)，无法判断是否在负责范围内。`);
        return;
      }

      // 这里的逻辑假设 id 是从0或1开始的连续或大致连续的数字，代表了主播在整个表中的顺序
      // currentOffset 是起始行号（0-indexed or 1-indexed based on your DB）
      // currentLimit 是数量
      // 注意：Supabase 的 range 是 [inclusive, inclusive]。你需要确认你的 offset/limit 语义
      // 例如，如果 does 表的 jp 字段是 "[100,550)"，意味着 offset=100, limit=450
      // 假设数据库 id 从 1 开始，则范围是 id >= 100 AND id < 550
      // 如果 currentOffset 是 0-indexed，那么范围是 anchor_index >= currentOffset AND anchor_index < currentOffset + currentLimit
      // **这个判断逻辑需要根据你数据库的实际情况和 does 表的语义来精确调整**
      // 为简化，我们假设 id 代表了行号，并且 currentOffset 是 0-indexed 的起始行号
      const anchorIndex = anchorDetails.id; // 或者其他代表顺序的字段的值
      // 假设 does 表的 currentOffset 是基于0的，currentLimit 是数量
      // 例如，如果 does 表是 [0, 100)，则 currentOffset = 0, currentLimit = 100
      // 这意味着处理 id 为 0 到 99 的主播
      // 如果 does 表是 [100, 200)，则 currentOffset = 100, currentLimit = 100
      // 这意味着处理 id 为 100 到 199 的主播
      // **重要**: 请确保这里的 anchorIndex 和 currentOffset/currentLimit 的比较逻辑
      // 与 `checkAndRestartMissedRecordings` 中 `.range(currentOffset, currentOffset + currentLimit - 1)`
      // 的逻辑一致，以避免重复录制或遗漏。
      // Supabase 的 .range(from, to) 是包含 from 和 to 的。所以 from 是 currentOffset, to 是 currentOffset + currentLimit - 1
      // 因此，anchorIndex（如果从0开始）应该 >= currentOffset 且 <= currentOffset + currentLimit - 1

      // 假设数据库中的 id 是从1开始的，而 currentOffset 是从0开始的索引。
      // 那么，如果 does.jp = "[1, 100)"， currentOffset = 1, currentLimit = 99.
      // Supabase range 会是 .range(1, 1+99-1) = .range(1,99)
      // 此时，anchorDetails.id 应该在 [currentOffset, currentOffset + currentLimit -1] 之间。
      // 如果 does.jp = "[0, 100)"， currentOffset = 0, currentLimit = 100.
      // Supabase range 会是 .range(0, 0+100-1) = .range(0,99)
      // 此时，anchorDetails.id 应该在 [currentOffset, currentOffset + currentLimit -1] 之间。

      const isInRange = anchorIndex >= currentOffset && anchorIndex < (currentOffset + currentLimit);
      // 注意：上面的 currentOffset + currentLimit 是开区间上限，对应 does 表的 " [start, end) " 格式
      // 如果 does 表的 end 是闭区间，则应该是 anchorIndex <= (currentOffset + currentLimit -1)
      // 根据您之前 updateRangeFromDoes 的解析，currentLimit = end - start，currentOffset = start
      // 例如 does 表为 [100, 550)，则 offset = 100, limit = 450.
      // 这意味着我们处理的是索引从 100 到 549 的条目。
      // 所以 anchorIndex 应该 >= 100 且 < 550.

      if (!isInRange) {
        log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) (顺序标识: ${anchorIndex}) 不在当前实例负责范围 [${currentOffset}, ${currentOffset + currentLimit})，跳过。`);
        return;
      }
      log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) (顺序标识: ${anchorIndex}) 在当前实例负责范围 [${currentOffset}, ${currentOffset + currentLimit}) 内。`);

    } catch (rangeCheckError) {
      log(LOG_LEVEL.ERROR, `[onAnchorChange] 检查主播 ${anchorId} 是否在负责范围内时出错: ${rangeCheckError.message}`);
      return; // 出错则不处理，避免潜在的并发问题
    }

    // 2. 检查是否已在录制 (理论上 is_recording 应该是 false，但双重检查)
    if (activeRecordings.has(anchorId)) {
      log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 已在 activeRecordings 中，可能已被其他逻辑启动，跳过。`);
      return;
    }

    // 3. 获取 stream_url 并开始录制
    const streamUrlData = newRecord.stream_url;
    if (!streamUrlData) {
      log(LOG_LEVEL.WARN, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 开播但 stream_url 为空，无法开始录制。`);
      // 可以在这里尝试调用一次 getLatestStreamUrl，或者依赖后续的 checkAndRestartMissedRecordings
      return;
    }

    log(LOG_LEVEL.INFO, `[onAnchorChange] 准备通过 anchors 表变化启动主播 ${anchorId} (${anchorName}) 的录制。`);
    await startRecording(anchorId, streamUrlData); // 确保 startRecording 是 async 或者能够处理 promise
    await incrementCount(anchorId, 'bcount'); // 记录一次启动尝试
  }
};

/**
 * 程序初始化逻辑
 */
const initializeProgram = async () => {
  // 检测yt-dlp版本
  try {
    const { stdout } = await execPromise('yt-dlp --version');
    console.log(`[${getCurrentTime()}] 使用系统 yt-dlp，版本: ${stdout.trim()}`);
  } catch (err) {
    console.error(`[${getCurrentTime()}] 无法检测 yt-dlp 版本，请确保已安装并添加到 PATH: ${err.message}`);
    // 可以选择在此处退出程序，或者让后续依赖 yt-dlp 的功能自然失败
    // process.exit(1); 
  }

  // 初始化时读取区间
  await updateRangeFromDoes();

  console.log(`[${getCurrentTime()}] 程序启动，开始监听数据库变更`);

  // 暂时禁用Redis消息订阅，专注数据库处理
  // setupRedisSubscriber();

  // 启动后检查一次遗漏录制
  await checkAndRestartMissedRecordings();

  // 新增：监听 anchors 表的变化
  supabase
    .channel('public-anchors-changes') // 给channel一个唯一的名字
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'anchors',
        // filter: 'status=eq.true' // 如果只想接收 status 变为 true 的事件，可以加filter
        // 但我们需要 oldRecord.status 来判断是否是从 false 变为 true，所以暂时不在这里加 filter
      },
      onAnchorChange
    )
    .subscribe((status, err) => {
      if (err) {
        log(LOG_LEVEL.ERROR, `订阅 anchors 表变化失败: ${JSON.stringify(err)}`);
      }
      log(LOG_LEVEL.INFO, `订阅 anchors 表变化成功，状态: ${status}`);
    });

  console.log(`[${getCurrentTime()}] 初始化操作完成，监听数据库变更`);
};

/**
 * 设置Redis订阅者，监听record频道
 */
function setupRedisSubscriber() {
  const subscriber = new Redis();
  
  subscriber.subscribe('record', (err) => {
    if (err) {
      console.error(`[${getCurrentTime()}] 订阅record频道失败: ${err.message}`);
      return;
    }
    console.log(`[${getCurrentTime()}] 成功订阅record频道，等待录制消息...`);
  });
  
  subscriber.on('message', async (channel, message) => {
    if (channel === 'record') {
      console.log(`[${getCurrentTime()}] 收到record频道消息: ${message.substring(0, 100)}...`);
      
      try {
        // 解析消息格式: start|anchorId|streamUrlData|newUrl
        const parts = message.split('|');
        if (parts.length < 3 || parts[0] !== 'start') {
          console.error(`[${getCurrentTime()}] 无效的record消息格式: ${message.substring(0, 100)}...`);
          return;
        }
        
        const command = parts[0];
        const anchorId = parts[1];
        const streamUrlData = parts[2];
        const newUrl = parts.length > 3 ? parts[3] : '';
        
        // 检查该主播是否已经在录制中
        if (activeRecordings.has(anchorId)) {
          console.log(`[${getCurrentTime()}] 主播 ${anchorId} 已经在录制中，跳过此消息`);
          return;
        }
        
        if (command === 'start') {
          // 开始录制
          console.log(`[${getCurrentTime()}] 开始录制主播 ${anchorId}, new_url: ${newUrl || '无'}`);
          
          // 如果有newUrl，将其添加到streamUrlData对象中
          let finalStreamUrlData = streamUrlData;
          if (newUrl && finalStreamUrlData) {
            try {
              if (typeof finalStreamUrlData === 'string' && !finalStreamUrlData.startsWith('http')) {
                // 尝试解析为对象，添加_new_url字段后重新序列化
                const parsed = JSON.parse(finalStreamUrlData);
                parsed._new_url = newUrl;
                finalStreamUrlData = JSON.stringify(parsed);
              } else if (typeof finalStreamUrlData === 'object') {
                finalStreamUrlData._new_url = newUrl;
              }
            } catch (e) {
              console.error(`[${getCurrentTime()}] 添加new_url失败: ${e.message}`);
            }
          }
          
          await startRecording(anchorId, finalStreamUrlData);
        }
      } catch (err) {
        console.error(`[${getCurrentTime()}] 处理record消息时出错: ${err.message}`);
      }
    }
  });
  
  subscriber.on('error', (err) => {
    console.error(`[${getCurrentTime()}] Redis订阅者错误: ${err.message}`);
  });
  
  // 优雅关闭时记得断开Redis连接
  process.on('SIGINT', async () => {
    console.log(`[${getCurrentTime()}] 关闭Redis订阅者连接...`);
    await subscriber.quit();
    // 其他关闭逻辑会在gracefulShutdown中处理
  });
}

// ----------------------------------------------------------------------------
// 全局健壮性：捕获未处理异常与 Promise 拒绝，防止整个进程崩溃
// ----------------------------------------------------------------------------

process.on('uncaughtException', (err) => {
  logError('捕获到未处理异常 (uncaughtException)', err);
  // 为避免进程陷入未知状态，可选择 process.exit(1)
  // 但用户需求是"不退出"，因此这里只记录。
});

process.on('unhandledRejection', (reason) => {
  if (reason instanceof Error) {
    logError('捕获到未处理 Promise 拒绝 (unhandledRejection)', reason);
  } else {
    log(LOG_LEVEL.ERROR, `捕获到未处理 Promise 拒绝: ${String(reason)}`);
  }
});

initializeProgram();
