







<!DOCTYPE html>
<html lang="zhCN">
<head>
    <meta charset="utf-8">
    <title>阿胜l的照片</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="format-detection" content="telephone=no"/>
    <link rel="stylesheet" href="//pic.finkapp.cn/asset/js/swiper-3.3.1/swiper.min.css">
    <script src="//pic.finkapp.cn/asset/js/flexible.js"></script>
    <style>
        *{
            margin:0;
            padding:0;
        }
        body{
            font-family: -apple-system,BlinkMacSystemFont,PingFang-SC-Regular,Hiragino Sans GB,Microsoft Yahei,Arial,sans-serif;
        }
        .header{
            height: 1.2rem;
            background-color: rgba(26, 26, 26, 0.7);
            position: fixed;
            top:0;
            left:0;
            width: 100%;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding:0 0.3rem;
            box-sizing: border-box;
        }
        .header-left {
            height: 0.7rem;
            width: 2.83rem;
            background:  url(//pic.finkapp.cn/asset/image/hashtag/logo2.png) no-repeat;
            background-size: 2.83rem 0.7rem;
        }
        .header-right{
            width: 1.48rem;
            height: 0.54rem;
            background:  url(//pic.finkapp.cn/asset/image/hashtag/download_button1.png) no-repeat;
            background-size:1.48rem 0.54rem;
         }

        .cover-wrapper.type-video video {
            width: 100%
        }

        .user-link {
            font-size: 20px;
            color: #1a1a1a;
            font-weight: 700;
            display: inline-block;
            max-width: 25%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: absolute;
            top: 1.4rem;
            left: 2.8rem;
        }


        .live-mian{
            padding-bottom: 2.6rem;
            margin-top:1.2rem;
        }
        .seepic{
            display: flex;
            justify-content: center;
            padding-top:0.4rem;
        }
        .seepic span{
            font-size: 0.26rem;
            color: #a9a9a9;
        }
        .seepic span:last-child{
            padding-left: 0.1rem;
            color: #FF4040;
            background: url(//pic.finkapp.cn/asset/image/hashtag/grey_arrow.png) no-repeat right 0.1rem ;
            background-size: 0.12rem 0.18rem;
            padding-right: 0.3rem;
        }

        .userinfo {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding:0 0.27rem 0 0.27rem;
            height: 1.62rem;
            /*border-bottom: 1px solid #F2F2F2;*/
            box-sizing: border-box;
        }
        .userinfo .openapp{
            width: 1.16rem;
            height: 0.54rem;
            line-height: 0.54rem;
            text-align: center;
            border: 1px solid #FF4040;
            border-radius: 0.27rem;
            color: #FF4040;;
            font-size: 0.28rem;
        }
        .userinfo-left-cent {
            display: flex;
            justify-content: start;
        }
        .userinfo-img{
            height: 0.9rem;
            width: 0.9rem;
            border-radius: 100%;
            display: block;
            overflow:hidden;
        }
        .userinfo-img img{
            height: 0.9rem;
            width: 0.9rem;
            display: block;
        }
        .userinfo-title {
            align-items: center;
            display: flex;
            padding-left:0.2rem ;
            flex:1;
        }
        .userinfo-title p span{
            font-size: 0.23rem;
            display: block;
        }
        .userinfo-title p span:first-child{
            font-size: 0.32rem;
            color: #1a1a1a;
            font-weight: bold;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
            overflow: hidden;
        }
        .userinfo-title p span:last-child{
            font-size: 0.24rem;
            color: #a9a9a9;
            margin-top: 0.03rem;
        }
        .userimg-box{
            background-color: #fff;
            position: relative;
            top: -0.3rem;
            box-sizing: border-box;
            z-index: 11;
        }
        .userimg-box-video{
            top: -0.25rem;
        }
        .userimg-box h3{
            font-size: 0.32rem;
            font-weight: normal;
            padding: 0.6rem 0.27rem 0 0.27rem;
            font-weight: bold;
        }

        .postinfo{
            margin:0 0.27rem;
        }
        .posinfo-title{
            font-size: 0.32rem;
            color: #1A1A1A;
            padding:0.1rem 0 0.1rem 0;
            font-weight: 600;
        }
        .postinfo-wen{
            font-size: 0.28rem;
            color:rgba(26,26,26,0.7);
            letter-spacing: 0;
            line-height: 0.45rem;
            white-space: pre-line;
        }
        .postinfo-tag{
            background: rgba(0, 0, 0, 0.05);
            border-radius: 0.4rem;
            display: table;
            margin-top: 0.2rem;
            overflow: hidden;
            padding:0.1rem 0.15rem;
        }
        .postinfo-tag-box{
            display: flex;
            align-items: center;
        }
        .postinfo-tag img{
            height: 0.3rem;
            width: 0.3rem;
            border-radius: 100%;
            float: left;
        }
        .postinfo-tag h2{
            font-size: 0.24rem;
            color: #1A1A1A;
            padding-left: 0.1rem;
            font-weight: normal;
            float: left;
        }
        .comment{
            margin-top: 0.2rem;
            border-top: 1px solid #F2F2F2;
        }
        .not-comment{
            width: 7.14rem;
            height: 0.8rem;
            line-height: 0.8rem;
            background: #F2F2F2;
            border: 0.02rem solid #C6C6C6;
            border-radius: 0.4rem;
            text-align: center;
            font-size: 0.28rem;
            color: #A9A9A9;
            margin: 0 auto;
            margin-top: 0.6rem;
        }
        .yes-comment{
            height: 0.8rem;
            line-height: 0.8rem;
            background: url(//pic.finkapp.cn/asset/image/hashtag/download_button3.png) no-repeat center;
            background-size:100%;
            font-size: 0.28rem;
            color: #FFFFFF;
            text-align: center;
            margin:0 0.27rem;
            margin-top: 0.4rem;

        }
        .comment-item{
            padding:0 0.27rem;
            margin-top:0.3rem;
        }
        .comment-item:first-child{
            padding:0 0.27rem;
            margin-top:0.35rem;
        }
        .comment-item2{
            margin-top:0.15rem;
        }
        .comment-user{
            height: 0.58rem;
        }
        .comment-wen{
            font-size: 0.26rem;
            color:rgba(26,26,26,0.7);
            padding:0.2rem 0 0.2rem 0.73rem;
            line-height: 0.5rem;
        }
        .comment-num{
            padding-left: 0.73rem;
            display: flex;
        }
        .comment-num span{
            font-size: 0.28rem;
            color: #A9A9A9;
            background: url(//pic.finkapp.cn/asset/image/hashtag/down_arrow.png) no-repeat right center;
            background-size: 0.17rem 0.08rem;
            display: inline-block;
            padding-right: 0.3rem;
        }
        .comment-user{
            display: flex;
            justify-content:space-between;
            align-items: center;
        }
        .comment-user-l{
            display: flex;
            justify-content:start;
            align-items: center;
        }
        .comment-user-l p{
            display: block;
            width: 0.58rem;
            height: 0.58rem;
            border-radius: 100%;
            overflow: hidden;
        }
        .comment-user-l span{
            font-size: 0.28rem;
            color: #1A1A1A;
            margin-left: 0.18rem;
            font-weight: bold;
        }
        .comment-user-r{
            font-size: 0.24em;
            display: flex;
            justify-content: end;
            align-items: center;
        }
        .comment-user-r span{
            color:rgba(26,26,26,0.7);
        }
        .comment-user-r span:nth-child(1){
            background: url(//pic.finkapp.cn/asset/image/hashtag/zan.png) no-repeat 0 center;
            background-size:0.24rem 0.24rem;
            padding-left: 0.35rem;
        }
        .comment-user-r span:nth-child(2){
            width: 0.08rem;
            height: 0.08rem;
            background: rgba(26,26,26,0.7);
            margin: 0 0.1rem;
            border-radius: 0.08rem;
        }


        .switch-box{
            padding:0.25em 0.2rem 0 0.2rem;
            column-count: 2;
            column-gap: 0.2rem;
            border-top: 1px solid #F2F2F2;
            margin-top: 0.2rem;
        }
        .item{
            display:grid;
            break-inside: avoid;
            -moz-page-break-inside: avoid;
            -webkit-column-break-inside: avoid;
            box-sizing: border-box;
        }
        .item-box {
            width: 100%;
            box-shadow: 0 0.04rem 0.08rem 0 rgb(0 0 0 / 4%);
            border-radius: 0.1rem;
            margin-bottom: 0.2rem;
        }

        .item-box-img {
            width: 100%;
            min-height: 1rem;
            background-color: #eee;
            border-bottom: none;
            box-sizing: border-box;
            border-radius: 0.1rem 0.1rem 0 0;
            position: relative;
        }
        .item-box-img img {
            width: 100%;
            height: 100%;
            display: block;
            box-sizing: border-box;
            border-radius: 0.1rem 0.1rem 0 0;
        }

        .item-info {
            border-top:none;
            padding: 0.2rem;
        }

        .item-title {
            font-size: 0.28rem;
            color: #1A1A1A;
            margin-bottom: 0.15rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-all;
            overflow: hidden;
        }
        .userinfo-left{
            padding-right: 0.2rem;
            flex: 1;
        }
        .item-user {
            display: flex;
            justify-content: space-between;
        }

        .item-user-img {
            display: flex;
            justify-content: start;
            align-items: center;
            height: 0.4rem;
        }

        .item-user-img p {
            width: 0.4rem;
            height: 0.4rem;
            border-radius: 100%;
            display: block;
            overflow: hidden;
        }

        .item-user-img span {
            font-size: 0.24rem;
            color: #A9A9A9;
            margin-left: 0.1rem;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
            overflow: hidden;
        }

        .fabulous {
            font-size: 0.28rem;
            color: #A9A9A9;
            background: url(//pic.finkapp.cn/asset/image/hashtag/zan.png) no-repeat 0 0.04rem;
            background-size: 0.27rem 0.27rem;
            padding-left: 0.34rem;
            height: 0.4rem;
            line-height: 0.4rem;
        }

        .app-go{
            display: flex;
            justify-content: center;
        }
        .app-go span{
            font-size: 0.32rem;
            color: #A9A9A9;
            letter-spacing: 0.46px;
            text-decoration:underline;
            padding-right: 0.2rem;
            background: url(//pic.finkapp.cn/asset/image/hashtag/grey_arrow.png) no-repeat right 0.16rem ;
            background-size: 0.12rem 0.18rem;
        }

        .app-download{
            width: 3.02rem;
            height: 1.24rem;
            position: fixed;
            bottom:0.6rem;
            left:50%;
            z-index: 100;
            margin-left:-1.51rem;
            background:  url(//pic.finkapp.cn/asset/image/hashtag/download_button2.png) no-repeat;
            background-size:3.02rem 1.24rem;
        }

        .live-video{
            height: 6.4rem;
            position: relative;
        }

        .swiper-container {
            width: 100%;
            height: 100%;
        }

        .swiper-slide {
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
        }
        .live-video .swiper-pagination{
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction {
            bottom: 0.6rem;
            left: 0;
            width: 100%;
        }
        .live-video .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            opacity: 0.7;
            background: #FFFFFF;
            border-radius: 100%;
        }
        .live-video .swiper-pagination-bullet-active {
            opacity: 1;
            width: 12px;
            height: 12px;
            background: #FF4040;
        }
        .launch-guid {
            position: fixed;
            display: none;
            z-index: 1010;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            background: rgba(0, 0, 0, .8);
            cursor: pointer
        }

        .launch-guid:before {
            width: 1.25rem;
            height: 1.85rem;
            background: url(//pic.finkapp.cn/asset/image/user/arrow.png) 0 0 no-repeat scroll;
            background-size: cover;
            content: " ";
            position: absolute;
            right: 1.1875rem;
            top: 0.8rem;
        }

        .launch-guid p {
            text-align: center;
            color: #fff;
            font-size: 0.59rem;
            line-height: 1.5;
            height: 2rem;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto
        }
    </style>
    
<script src="//pic.finkapp.cn/asset/js/jquery-1.11.0.min.js"></script>

<script src="//pic.finkapp.cn/asset/js/jsrender.min.js"></script>
</head>
<body>

<div class="header">
    <div class="header-left"> </div>
    <div class="header-right"></div>
</div>

<div class="live-mian">
    
        
        
                <div class="live-video">
                    <div class="swiper-container">
                        <div class="swiper-wrapper">
                            
                                <div class="swiper-slide" style="background:url(https://pic1.finkapp.cn/k-tobhee2M9qRPIj7cLlMQ?imageMogr2/thumbnail/750x/quality/70/format/jpeg) no-repeat center;
                                        background-size:contain;"></div>
                            
                                <div class="swiper-slide" style="background:url(https://pic1.finkapp.cn/VST7sFCsUbhqRPIj7cLlMQ?imageMogr2/thumbnail/750x/quality/70/format/jpeg) no-repeat center;
                                        background-size:contain;"></div>
                            
                                <div class="swiper-slide" style="background:url(https://pic1.finkapp.cn/BfYbzNotEJhqRPIj7cLlMQ?imageMogr2/thumbnail/750x/quality/70/format/jpeg) no-repeat center;
                                        background-size:contain;"></div>
                            
                        </div>
                        
                             <div class="swiper-pagination"></div>
                        
                    </div>
                </div>
        
    

    <div class="userimg-box ">
        <div class="seepic">
            <span>打开翻咔APP，查看</span>
            <span>高清原图</span>
        </div>
        <div class="userinfo">
            <div class="userinfo-left">
                <div class="userinfo-left-cent">
                    <div class="userinfo-img" style="background:url(https://pic1.finkapp.cn/uDUyidKhDttqRPIj7cLlMQ?imageMogr2/thumbnail/250x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></div>
                    <div class="userinfo-title">
                        <p>
                            <span>阿胜l</span>
                            <span>25651关注</span>
                        </p>
                    </div>
                </div>
            </div>
            <span class="openapp">关注</span>
        </div>
        <div class="postinfo">
            <div class="posinfo-title"></div>
            <div class="postinfo-wen">3张好看的照片</div>
            
                <div class="postinfo-tag">
                    <div class="postinfo-tag-box">
                        <img src="//pic.finkapp.cn/asset/image/hashtag/huati.png" alt="">
                        <h2>限量版的我</h2>
                    </div>
                </div>
            
        </div>
        <h3>全部评论</h3>
        
            
                <div class="comment">
                    
                        <div class="comment-item ">
                            <div class="comment-user">
                                <div class="comment-user-l">
                                    <p style="background:url(https://pic1.finkapp.cn/Dqvhb4V5j-5qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                    <span>暮晚不爱吃</span>
                                </div>
                                <div class="comment-user-r">
                                    <span class="cfabulous">点赞</span>
                                    <span></span>
                                    <span class="reply">回复</span>
                                </div>
                            </div>
                            <div class="comment-wen">小小阿胜，快进我碗里🌚</div>
                            
                        </div>
                    
                        <div class="comment-item ">
                            <div class="comment-user">
                                <div class="comment-user-l">
                                    <p style="background:url(https://pic1.finkapp.cn/Dqvhb4V5j-5qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                    <span>暮晚不爱吃</span>
                                </div>
                                <div class="comment-user-r">
                                    <span class="cfabulous">点赞</span>
                                    <span></span>
                                    <span class="reply">回复</span>
                                </div>
                            </div>
                            <div class="comment-wen">😂</div>
                            
                        </div>
                    
                    <div class="yes-comment">打开翻咔APP，查看全部2条评论</div>
                </div>
            
            
        
        
            <h3>推荐动态</h3>
            <div class="switch-box">
                

                    <div class="item" data-post-id = "ShpaCfc2WCFPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/2fj8IOvZuc5qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/BxTnj4v0PoBqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>五十而浪</span>
                                    </div>
                                    <div class="fabulous">32</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "k49aXAiKLXVPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/veDWVQrefrVqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/hdAxnk6kcohqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>winery</span>
                                    </div>
                                    <div class="fabulous">72</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "KFInX9QGT1xPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/255ShOUk8FxqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/78uBVFCql19qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>布隆希达</span>
                                    </div>
                                    <div class="fabulous">18</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "y7ZhGOE_KCFPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/2NukzyHtjd5qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/IRPPSBwI6x1qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>羊驼DD</span>
                                    </div>
                                    <div class="fabulous">30</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "fDrcuya8KMFPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/NiLcjaSkpm9qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/qATb4TthBStqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>别克Бек</span>
                                    </div>
                                    <div class="fabulous">45</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "lfIEUkJJGC5PrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/uUfuaPfbQjZqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/UlsTzS8358hqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>杰森_JaaaaYsOn</span>
                                    </div>
                                    <div class="fabulous">165</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "r0rKS6eTTQ1PrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/m5UVIy2d4I5qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/AGnVm1hGSmFqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>#Kekee</span>
                                    </div>
                                    <div class="fabulous">18</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "T14pOi7V-RtPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/XLz8e-zMBY1qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/4pY2AY7nJytqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>——————/////</span>
                                    </div>
                                    <div class="fabulous">23</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "rVq64jWXkSdPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/N_T6VelkLPpqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/uRwzdEC4yLtqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>Kevin_NX</span>
                                    </div>
                                    <div class="fabulous">82</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "y6PkUNxa_IhPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/TfW-f-V5lgFqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/KE7RbIjHYStqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>p碳水超人</span>
                                    </div>
                                    <div class="fabulous">5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "W-0uPtezXz9PrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/9kykO-lsPKxqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/3egIy1FhUdFqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>冷萃不加冰🇨🇳</span>
                                    </div>
                                    <div class="fabulous">60</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "ZjiHVmmjcj5PrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/SCsz1NOIHtdqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/is_K7Fv2ZvxqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>-Ted🦦</span>
                                    </div>
                                    <div class="fabulous">44</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "8Eo-RnIC95hPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/3Y2kJrTFSM9qRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/fE1s3aS9pM5qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>顾海BLY</span>
                                    </div>
                                    <div class="fabulous">12</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "aW4athQSihNPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/BdmkCnROAqlqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/kvC1kI_h96RqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>文武～</span>
                                    </div>
                                    <div class="fabulous">110</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "ph4Z9rJ-FuJPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/_9pQTJknsBxqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/GldhqLcxWUBqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>-Birdy-</span>
                                    </div>
                                    <div class="fabulous">32</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "7xEck54y7rZPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/byvz1gHcK7xqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/V41dKxAUi69qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>亀·淳一</span>
                                    </div>
                                    <div class="fabulous">9</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "4jiAKeg4gPJPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/xnFWrvizlOtqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/uzZejGmMMmdqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>Benjaminck</span>
                                    </div>
                                    <div class="fabulous">82</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "KRgarmOAyhRPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/FqVKs6v274lqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/qPE4A7IUsrhqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>petto_</span>
                                    </div>
                                    <div class="fabulous">52</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "chRg3F-upCRPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/GXtQ8_tgPzdqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/hHabyip-c19qRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>一亲就赢</span>
                                    </div>
                                    <div class="fabulous">62</div>
                                </div>
                            </div>
                        </div>
                    </div>
                

                    <div class="item" data-post-id = "8BnuXstCp-JPrCYDWVbkEQ">
                        <div class="item-box">
                            <div class="item-box-img">
                                <img src="https://pic1.finkapp.cn/6JaHWkBVBOpqRPIj7cLlMQ?imageMogr2/thumbnail/480x/quality/70/format/jpeg">
                            </div>
                            <div class="item-info">
                                
                                <div class="item-user">
                                    <div class="item-user-img">
                                        <p style="background:url(https://pic1.finkapp.cn/jR21Io82Z6lqRPIj7cLlMQ?imageMogr2/thumbnail/150x/quality/70/format/jpeg) center no-repeat;background-size: cover;"></p>
                                        <span>姚小地</span>
                                    </div>
                                    <div class="fabulous">50</div>
                                </div>
                            </div>
                        </div>
                    </div>
                
            </div>
        
    </div>





</div>
<!--下载app-->
<div class="app-download"></div>

<div id="launch-guid" class="launch-guid">
    <p>点击右上角<br/>选择“在Safari中打开”
    </p>
</div>








<script>
    try {
        (function (win, export_obj) {
            win['TeaAnalyticsObject'] = export_obj;
            if (!win[export_obj]) {
                function _collect() {
                    _collect.q.push(arguments);
                }

                _collect.q = _collect.q || [];
                win[export_obj] = _collect;
            }
            win[export_obj].l = +new Date();
        })(window, 'collectEvent');
    } catch (e) {
    }
</script>
<script>

    //è·åurlä¸­åæ°
    function getQueryValue(key)
    {
        var url = location.search;
        var theRequest = new Object();
        if (url.indexOf("?") != -1)
        {
            var str = url.substr(1);
            strs = str.split("&");
            for (var i = 0; i < strs.length; i++)
            {
                theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
            }
        }
        var value = theRequest[key];
        return value;
    }

    /**
     * å¤æ­å½åçæ¬æ¯å¦å¤§äºç­äºæä¸ªçæ¬
     * false: å°äºç®æ çæ¬èçæ¬ä¸è½ç¨,
     * true: å¤§äºç­äºç®æ çæ¬
     * åæ°è¯´æï¼
     * @param {String} targetVersion //éè¦éå¶ççæ¬å·,
     * ä½¿ç¨æ¹å¼ï¼
     * versionCompare('5.9.20')
     */
    function versionCompare(targetVersion){
        var currentVersion = '1.6.0'; //å½åå®¢æ·ç«¯ä¼ å¥çæ¬
        if (!currentVersion || !targetVersion) return
        currentVersion = currentVersion
            .replace(/[^0-9]/gi, ',')
            .split(',')
            .filter((str) => !!str)
        targetVersion = targetVersion
            .replace(/[^0-9]/gi, ',')
            .split(',')
            .filter((str) => !!str)
        if (currentVersion.join() == targetVersion.join()) {
            return true
        }
        for (let i = 0; i < targetVersion.length; i++) {
            currentVersion[i] = Number(currentVersion[i])
            targetVersion[i] = Number(targetVersion[i])
            if (currentVersion[i] > targetVersion[i]) {
                return true
            } else if (currentVersion[i] < targetVersion[i]) {
                return false
            } else if (currentVersion[i] == targetVersion[i]) {
                continue
            }
        }
    }

    
    function copyText(text){
        try {
            // æ°å­æ²¡æ .length ä¸è½æ§è¡selectText éè¦è½¬åæå­ç¬¦ä¸²
            const textString = text.toString()
            let input = document.querySelector('#copyUrl2')

            if (!input) {
                input = document.createElement('input')
                input.id = '#copyUrl2'
                input.readOnly = 'readOnly' // é²æ­¢iosèç¦è§¦åé®çäºä»¶
                input.style.position = 'absolute'
                input.style.left = '-1000px'
                input.style.zIndex = '-1000'
                document.body.appendChild(input)
            }

            input.value = textString
            // ioså¿é¡»åéä¸­æå­ä¸ä¸æ¯æ input.select();
            selectText(input, 0, textString.length)
            if (document.execCommand('copy')) {
                document.execCommand('copy')
                // alert('å·²å¤å¶å°ç²è´´æ¿')
            } else {
                console.log('ä¸å¼å®¹')
            }
            input.blur()
            // Toast.info('å¤å¶æå')
        } catch (e) {
            Toast.info('å¤å¶å¤±è´¥')
            console.log('å¤å¶å¤±è´¥')
        }

        // inputèªå¸¦çselect()æ¹æ³å¨è¹æç«¯æ æ³è¿è¡éæ©ï¼æä»¥éè¦èªå·±å»åä¸ä¸ªç±»ä¼¼çæ¹æ³
        // éæ©ææ¬ãcreateTextRange(setSelectionRange)æ¯inputæ¹æ³
        function selectText(textbox, startIndex, stopIndex) {
            if (textbox.createTextRange) {
                //ie
                const range = textbox.createTextRange()
                range.collapse(true)
                range.moveStart('character', startIndex) //èµ·å§åæ 
                range.moveEnd('character', stopIndex - startIndex) //ç»æåæ 
                range.select() //ä¸å¼å®¹è¹æ
            } else {
                //firefox/chrome
                textbox.setSelectionRange(startIndex, stopIndex)
                textbox.focus()
            }
        }
    }

    
    var weTrackEvent = function (event_name, params) {
       console.log('')
    }

    
    function isIOS(){
        var u = navigator.userAgent;
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //iosç»ç«¯
        return isiOS;
    }

    // ä¸­å°åç¹æ¹æ³
    var bluedCollectEvent = function(extra) {
        fetch("https://finka-web.irisdt.cn", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                common:{
                    uid_str: "",  //ç¨æ·idå­ç¬¦ä¸²
                    platform: isIOS() ? 'ios_china' : 'android_china', //å¹³å°
                    client_time: new Date().getTime(), //æ¥å¿æ¶é´
                    url: document.URL, //å½åé¡µé¢URL
                    referrer: document.referrer, //ä¸ä¸ªé¡µé¢çURL
                    app_version:"1.6.0", //è½¯ä»¶çæ¬
                    device:"1", //è®¾å¤åçåå·ç­ä¿¡æ¯
                    os_version:"", //ç³»ç»çæ¬
                    screen_width: Number(),
                    screen_high: Number(),
                },
                extra: extra
            })
        })
    }

</script>
<script src="//pic.finkapp.cn/asset/js/swiper-3.3.1/swiper.min.js"></script>
<script src="//pic.finkapp.cn/asset/js/clipboard.min.js"></script>
<script  type="text/javascript">
    $(function () {

        var clipVal = '';

        //获取剪切板口令
        getClipboardData()

        var swiper = new Swiper('.swiper-container', {
            pagination: '.swiper-pagination',
            paginationClickable: true
        });

        $('#type-video').click(function () {
            var $video = $(this).find('video');
            $video.get(0).play();
        });

        //个人主页分享落地页初始化埋点
        buryingPoint({event:"SHARE_PAGE_SHOW"})

        //打开app按钮
        $('.header-right').on('click', function () {
            clipboardFun({name:".header-right"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"打开App",position_name:"顶部浮层"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击看清原图
        $('.seepic').on('click', function () {
            clipboardFun({name:".seepic"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"打开App",position_name:"页面主体"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击关注按钮
        $('.openapp').on('click', function () {
            clipboardFun({name:".openapp"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"关注",position_name:"头像右侧"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击话题标题
        $('.postinfo-tag').on('click', function () {
            clipboardFun({name:".postinfo-tag"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"话题",position_name:"页面主体"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击点赞小图标
        $('.cfabulous').on('click', function () {
            clipboardFun({name:".cfabulous"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"点赞",position_name:"全部评论"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击回复
        $('.reply').on('click', function () {
            clipboardFun({name:".reply"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"回复",position_name:"全部评论"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击查看评论
        $('.comment-num').on('click', function () {
            clipboardFun({name:".comment-num"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"评论",position_name:"全部评论"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击打开翻咔APP查看全部评论
        $('.yes-comment,.not-comment').on('click', function () {
            clipboardFun({name:".yes-comment"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"打开App",position_name:"全部评论"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击相关动态卡片
        $('.switch-box .item').on('click', function () {
            var postId = $(this).attr('data-post-id') || "";
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"动态",position_name:"推荐位"})
            $.ajax({
                url : "/activity/off-site/passphrase",
                data : {
                    contentType : 2, //页面类型
                    shareUserId : 'YUZzqAXuczM', //分享用户id
                    contentId: postId, //内容id
                    ownerId : 'LX5ow083px8', //内容拥有者id
                    shareChannel: '', //分享渠道
                },
                type : "POST",
                context : this,
                success: function(r) {
                    if(r.success){
                        setTimeout(() => {
                            copyText(r.data.token)
                            sharePage(postId)
                        }, 100)
                    }
                }
            });
        });

        // 点击打开翻咔APP查看全部动态
        $('.app-go').on('click', function () {
            clipboardFun({name:".app-go"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"打开App",position_name:"推荐位"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        // 点击底部浮层下载按钮
        $('.app-download').on('click', function () {
            clipboardFun({name:".app-download"})
            buryingPoint({event:"SHARE_PAGE_CLICK",button_name:"打开App",position_name:"底部浮层"})
            setTimeout(() => {
                sharePage()
            }, 0)
        });

        //隐藏弹框
        $('#launch-guid').on('click', function () {
            $(this).hide()
        });
    });

    //获取口令
    function getClipboardData(){
        $.ajax({
            url : "/activity/off-site/passphrase",
            data : {
                contentType : 2, //页面类型
                shareUserId : 'YUZzqAXuczM', //分享用户id
                contentId: 'eLO6g3CfMPFPrCYDWVbkEQ', //内容id
                ownerId : 'LX5ow083px8', //内容拥有者id
                shareChannel: '', //分享渠道
            },
            type : "POST",
            context : this,
            success: function(r) {
                if(r.success){
                    clipVal = r.data.token
                }
            }
        });
    }

    //剪切板
    function clipboardFun(cliParamet) {
        new ClipboardJS(cliParamet.name, {
            text: function () {
                return clipVal;
            },
        });
    }

    // 埋点
    function buryingPoint(paramet) {
        bluedCollectEvent({
            event:paramet.event,
                share_user_id: 'YUZzqAXuczM', //分享者ID
                owner_id: 'LX5ow083px8', //内容拥有者ID
                owner_nick_name: '阿胜l',	//内容拥有者昵称
                content_type: '图片', //内容类型
                content_id: 'eLO6g3CfMPFPrCYDWVbkEQ', //内容ID
                share_type: '',  // 分享方式  字符串  微博、微信好友、朋友圈、QQ、qzone、复制链接
                button_name: paramet.button_name ? paramet.button_name : '',
                position_name: paramet.position_name ? paramet.position_name : '',
            });
    }

    // 分享数据
    function sharePage(postId){
        console.log(postId)
        //weibo环境下
        if(false){
            $('#launch-guid').show();
            return
        }
        var yybUrl = "http://a.app.qq.com/o/simple.jsp?pkgname=com.cupidapp.live&ckey=CK1493311993591&android_schema="; // 应用宝地址
        if(postId == undefined){
            var pageUrl = 'finka2020://post/' + 'eLO6g3CfMPFPrCYDWVbkEQ' + '?shareby=' + 'YUZzqAXuczM';// 页面地址
        }else{
            var pageUrl = 'finka2020://post/' + postId + '?shareby=' + 'YUZzqAXuczM';// 页面地址
        }
        //安卓环境下
        if(false){
            if(false||false){
                window.location.href = yybUrl + pageUrl;
            }else{
                window.location.href = pageUrl;
                downloader = setTimeout(function(){
                    window.location.href ="https://www.finka.cn/apk-promotion/self/increase"
                }, 3000);
            }
        }

        //判断安卓是否安装app
        document.addEventListener('visibilitychange webkitvisibilitychange', function () {
            // 如果页面隐藏，推测打开scheme成功，清除下载任务
            if (document.hidden || document.webkitHidden) {
                clearTimeout(downloader);
            }
        });

        window.addEventListener('pagehide', function() {
            clearTimeout(downloader);
        });

        if(true){
            window.location.href = 'https://finkapp.cn/native/universal-link-sharepost?url=' + pageUrl;
        }
    }
</script>


</body>
</html>