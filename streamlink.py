import subprocess
import json
import re
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from operator import itemgetter
from typing import Union, Dict, Any, List
import urllib.parse
import random
import urllib.request
import urllib.error
import gzip

no_proxy_handler = urllib.request.ProxyHandler({})
opener = urllib.request.build_opener(no_proxy_handler)

def get_bilibili_live_info(url, room_id, anchor_id):
    room_info = get_bilibili_room_info(url)
    anchor_name = room_info.get('anchor_name', '')
    status = room_info.get('live_status', False)
    title = room_info.get('title', '')  # 获取直播标题
    
    # 优先使用本地API获取stream_url
    stream_url = get_bilibili_stream_url_from_api(room_id)
    
    # 如果本地API未能成功获取stream_url，则使用streamlink获取
    if not stream_url:
        stream_url = get_bilibili_stream_url(url)
    
    return {
        'anchor_id': anchor_id,
        'anchor_name': anchor_name,
        'status': status,
        'title': title,
        'stream_url': stream_url
    }

def get_bilibili_stream_url_from_api(room_id):
    """
    通过本地API接口获取stream_url.
    要求返回格式为data->data->durl数组:
    durl: [
        {
          "url": "xxx",
          ...
        },
        {
          "url": "yyy",
          ...
        }
    ]
    最终stream_url需要拼接成 first_url[second_url] 的形式（如果存在第二个URL）
    """
    local_api_url = f"http://localhost:8080/api/bilibili/web/fetch_live_videos?room_id={room_id}"
    try:
        response_str = get_req(local_api_url)
        response_data = json.loads(response_str)
        # 判断返回数据结构是否正确
        # {
        #   "code": 200,
        #   "router": "/api/bilibili/web/fetch_live_videos",
        #   "data": {
        #     "code": 0,
        #     "message": "0",
        #     "ttl": 1,
        #     "data": {
        #       "durl": [
        #         { "url": "xxx" },
        #         { "url": "yyy" }
        #       ]
        #     }
        #   }
        # }
        if response_data.get('code') == 200:
            inner_data = response_data.get('data', {})
            if inner_data.get('code') == 0:
                bilibili_data = inner_data.get('data', {})
                durl_list = bilibili_data.get('durl', [])
                if durl_list:
                    # 至少有一个url
                    first_url = durl_list[0].get('url')
                    if not first_url:
                        # 第一个url都为空，直接返回None表示获取失败
                        return None
                    # 如果有第二个url，则拼接为 first_url[second_url]
                    if len(durl_list) > 1 and durl_list[1].get('url'):
                        second_url = durl_list[1]['url']
                        return f"{first_url}[{second_url}]"
                    else:
                        # 只有一个url
                        return first_url
        return None
    except Exception as e:
        print(f"Failed to fetch stream_url from local API: {e}")
        return None


def get_bilibili_stream_url(url):
    try:
        # 使用 streamlink 获取 bilibili 的直播流地址
        result = subprocess.run(['streamlink', '--stream-url', url, 'best'], 
                                capture_output=True, text=True, check=True)
        
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e}")
        return None
    except FileNotFoundError:
        print("streamlink command not found. Please make sure it's installed and in your PATH.")
        return None

def get_stream_info(url, anchor_id):
    try:
        stream_data = get_twitchtv_stream_data(url)
        anchor_name = stream_data.get('anchor_name', '')
        status = stream_data.get('is_live', False)
        title = ''  # 暂无标题信息
        play_url_list = stream_data.get('play_url_list', [])
        stream_url = play_url_list[0] if play_url_list else ''
        return 'Twitch', anchor_name, status, title, anchor_id, stream_url
    except Exception as e:
        return 'Twitch', '', None, '', anchor_id, ''

def get_live_info(platform, url, anchor_id):
    if platform == 'bilibili':
        # 从URL中获取room_id
        match = re.search(r'https?://live\.bilibili\.com/(\d+)', url)
        if match:
            room_id = match.group(1)
            result = get_bilibili_live_info(url, room_id, anchor_id)
            return result
        else:
            return f"错误:不支持的Bilibili链接格式\n{url}"
    elif platform == 'twitch':
        platform_name, anchor_name, status, title, anchor_id, stream_url = get_stream_info(url, anchor_id)
        return {
            'anchor_id': anchor_id,
            'anchor_name': anchor_name,
            'status': status,
            'title': title,
            'stream_url': stream_url
        }
    else:
        return f"错误:不支持的平台\n{platform}"

def get_twitchtv_room_info(url: str, token: str, proxy_addr: Union[str, None] = None,
                           cookies: Union[str, None] = None):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
        'Accept-Language': 'zh-CN',
        'Referer': 'https://www.twitch.tv/',
        'Client-Id': '******************************',
        'Client-Integrity': token,
        'Content-Type': 'text/plain;charset=UTF-8',
    }
    if cookies:
        headers['Cookie'] = cookies
    uid = url.split('?')[0].rsplit('/', maxsplit=1)[-1]

    data = [
        {
            "operationName": "ChannelShell",
            "variables": {
                "login": uid
            },
            "extensions": {
                "persistedQuery": {
                    "version": 1,
                    "sha256Hash": "580ab410bcd0c1ad194224957ae2241e5d252b2c5173d8e0cce9d32d5bb14efe"
                }
            }
        },
    ]

    json_str = get_req('https://gql.twitch.tv/gql', proxy_addr=proxy_addr, headers=headers, json_data=data, abroad=True)
    json_data = json.loads(json_str)
    user_data = json_data[0]['data']['userOrError']
    login_name = user_data["login"]
    nickname = f"{user_data['displayName']}-{login_name}"
    status = True if user_data['stream'] else False
    return nickname, status

def get_twitchtv_stream_data(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None) -> Dict[str, Any]:
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US',
        'Referer': 'https://www.twitch.tv/',
        'Client-ID': '******************************',
    }

    if cookies:
        headers['Cookie'] = cookies
    uid = url.split('?')[0].rsplit('/', maxsplit=1)[-1]

    data = {
        "operationName": "PlaybackAccessToken_Template",
        "query": "query PlaybackAccessToken_Template($login: String!, $isLive: Boolean!, $vodID: ID!, $isVod: Boolean!, $playerType: String!) {  streamPlaybackAccessToken(channelName: $login, params: {platform: \"web\", playerBackend: \"mediaplayer\", playerType: $playerType}) @include(if: $isLive) {    value    signature   authorization { isForbidden forbiddenReasonCode }   __typename  }  videoPlaybackAccessToken(id: $vodID, params: {platform: \"web\", playerBackend: \"mediaplayer\", playerType: $playerType}) @include(if: $isVod) {    value    signature   __typename  }}",
        "variables": {
            "isLive": True,
            "login": uid,
            "isVod": False,
            "vodID": "",
            "playerType": "site"
        }
    }

    json_str = get_req('https://gql.twitch.tv/gql', proxy_addr=proxy_addr, headers=headers, json_data=data, abroad=True)
    json_data = json.loads(json_str)
    token = json_data['data']['streamPlaybackAccessToken']['value']
    sign = json_data['data']['streamPlaybackAccessToken']['signature']

    anchor_name, live_status = get_twitchtv_room_info(url=url, token=token, proxy_addr=proxy_addr, cookies=cookies)
    result = {"anchor_name": anchor_name, "is_live": live_status}
    if live_status:
        play_session_id = random.choice(["bdd22331a986c7f1073628f2fc5b19da", "064bc3ff1722b6f53b0b5b8c01e46ca5"])
        params = {
            "acmb": "e30=",
            "allow_sourc": "true",
            "browser_family": "firefox",
            "browser_version": "124.0",
            "cdm": "wv",
            "fast_bread": "true",
            "os_name": "Windows",
            "os_version": "NT%2010.0",
            "p": "3553732",
            "platform": "web",
            "play_session_id": play_session_id,
            "player_backend": "mediaplayer",
            "player_version": "1.28.0-rc.1",
            "playlist_include_framerate": "true",
            "reassignments_supported": "true",
            "sig": sign,
            "token": token,
            "transcode_mode": "cbr_v1"
        }
        access_key = urllib.parse.urlencode(params)
        m3u8_url = f'https://usher.ttvnw.net/api/channel/hls/{uid}.m3u8?{access_key}'
        play_url_list = get_play_url_list(m3u8=m3u8_url, proxy=proxy_addr, header=headers, abroad=True)
        result['m3u8_url'] = m3u8_url
        result['play_url_list'] = play_url_list

    return result

def get_bilibili_room_info(url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None) -> Dict[str, Any]:
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:127.0) Gecko/20100101 Firefox/127.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
    }
    if cookies:
        headers['Cookie'] = cookies
    
    try:
        room_id = url.split('?')[0].rsplit('/', maxsplit=1)[1]

        json_str = get_req(f'https://api.live.bilibili.com/room/v1/Room/room_init?id={room_id}',
                           proxy_addr=proxy_addr, headers=headers)
        room_info = json.loads(json_str)
        uid = room_info['data']['uid']
        live_status = True if room_info['data']['live_status'] == 1 else False
        
        api = f'https://api.live.bilibili.com/live_user/v1/Master/info?uid={uid}'
        json_str2 = get_req(url=api, proxy_addr=proxy_addr, headers=headers)
        anchor_info = json.loads(json_str2)
        anchor_name = anchor_info['data']['info']['uname']
        
        # 获取直播标题
        info_api = f'https://api.live.bilibili.com/xlive/web-room/v1/index/getInfoByRoom?room_id={room_id}'
        json_str3 = get_req(url=info_api, proxy_addr=proxy_addr, headers=headers)
        room_data = json.loads(json_str3)
        title = room_data['data']['room_info']['title']
        
        return {"anchor_name": anchor_name, "live_status": live_status, "room_url": url, "title": title}
    except Exception as e:
        print(f"Error in get_bilibili_room_info: {e}")
        return {"anchor_name": '', "live_status": False, "room_url": url, "title": ''}

def get_play_url_list(m3u8: str, proxy: Union[str, None] = None, header: Union[dict, None] = None,
                      abroad: bool = False) -> List[str]:
    resp = get_req(url=m3u8, proxy_addr=proxy, headers=header, abroad=abroad)
    play_url_list = []
    for i in resp.split('\n'):
        if i.startswith('https://'):
            play_url_list.append(i.strip())
    if not play_url_list:
        for i in resp.split('\n'):
            if i.strip().endswith('m3u8'):
                play_url_list.append(i.strip())
    bandwidth_pattern = re.compile(r'BANDWIDTH=(\d+)')
    bandwidth_list = bandwidth_pattern.findall(resp)
    url_to_bandwidth = {url: int(bandwidth) for bandwidth, url in zip(bandwidth_list, play_url_list)}
    play_url_list = sorted(play_url_list, key=lambda url: url_to_bandwidth[url], reverse=True)
    return play_url_list

def get_req(
        url: str,
        proxy_addr: Union[str, None] = None, 
        headers: Union[dict, None] = None,
        data: Union[dict, bytes, None] = None,
        json_data: Union[dict, list, None] = None,
        timeout: int = 20,
        abroad: bool = False,
        content_conding: str = 'utf-8',
        redirect_url: bool = False,
) -> Union[str, Any]:
    if headers is None:
        headers = {}
    try:
        if proxy_addr:
            proxies = {
                'http': proxy_addr,
                'https': proxy_addr
            }
            if data or json_data:
                response = requests.post(url, data=data, json=json_data, headers=headers, proxies=proxies,
                                         timeout=timeout)
            else:
                response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            if redirect_url:
                return response.url
            resp_str = response.text
        else:
            if data and not isinstance(data, bytes):
                data = urllib.parse.urlencode(data).encode(content_conding)
            if json_data and isinstance(json_data, (dict, list)):
                data = json.dumps(json_data).encode(content_conding)

            req = urllib.request.Request(url, data=data, headers=headers)

            try:
                if abroad:
                    response = urllib.request.urlopen(req, timeout=timeout)
                else:
                    response = opener.open(req, timeout=timeout)
                if redirect_url:
                    return response.url
                content_encoding = response.info().get('Content-Encoding')
                try:
                    if content_encoding == 'gzip':
                        with gzip.open(response, 'rt', encoding=content_conding) as gzipped:
                            resp_str = gzipped.read()
                    else:
                        resp_str = response.read().decode(content_conding)
                finally:
                    response.close()

            except urllib.error.HTTPError as e:
                if e.code == 400:
                    resp_str = e.read().decode(content_conding)
                else:
                    raise
            except urllib.error.URLError as e:
                print("URL Error:", e)
                raise
            except Exception as e:
                print("An error occurred:", e)
                raise

    except Exception as e:
        resp_str = str(e)

    return resp_str
