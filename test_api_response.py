#!/usr/bin/env python3
"""
测试API响应，获取完整的API返回数据
"""

import json
import logging
from finka import FinkaAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_user_posts_api():
    """测试获取用户作品的API"""
    api = FinkaAPI()
    
    # 使用阿胜l的ID，他有视频作品
    user_id = "LX5ow083px8"
    user_name = "阿胜l"
    
    logger.info(f"测试获取用户作品API")
    logger.info(f"用户: {user_name} (ID: {user_id})")
    logger.info(f"API端点: GET /feed/user/v3")
    logger.info(f"参数: count=10, uid={user_id}")
    
    # 调用API
    result = api.get_user_posts(user_id, count=10)
    
    if result:
        # 保存完整的API响应
        with open('api_response_user_posts.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ API调用成功")
        logger.info(f"响应已保存到: api_response_user_posts.json")
        
        # 分析响应结构
        if result.get("success"):
            data = result.get("data", {})
            posts = data.get("list", [])
            
            logger.info(f"\n响应分析:")
            logger.info(f"  成功状态: {result.get('success')}")
            logger.info(f"  作品数量: {len(posts)}")
            logger.info(f"  有更多: {data.get('hasMore')}")
            logger.info(f"  下一页游标: {data.get('nextCursorId', 'None')}")
            
            # 分析每个作品的结构
            for i, post in enumerate(posts[:3], 1):
                logger.info(f"\n  作品 {i}:")
                logger.info(f"    ID: {post.get('postId', post.get('id', ''))}")
                logger.info(f"    类型: {post.get('type')}")
                logger.info(f"    描述: {post.get('description', '')[:50]}...")
                
                # 检查媒体字段
                has_image = 'image' in post
                has_images = 'images' in post
                has_video = 'video' in post
                has_videos = 'videos' in post
                
                logger.info(f"    字段存在:")
                logger.info(f"      image: {has_image}")
                logger.info(f"      images: {has_images}")
                logger.info(f"      video: {has_video}")
                logger.info(f"      videos: {has_videos}")
                
                if has_images:
                    logger.info(f"      images数量: {len(post.get('images', []))}")
                if has_videos:
                    logger.info(f"      videos数量: {len(post.get('videos', []))}")
        else:
            logger.error(f"API返回失败: {result}")
    else:
        logger.error("API调用失败，无返回数据")
    
    return result


def test_following_list_api():
    """测试获取关注列表的API"""
    api = FinkaAPI()
    
    logger.info(f"\n测试获取关注列表API")
    logger.info(f"API端点: POST /user/match/liked/v3/active/v3")
    logger.info(f"参数: count=5")
    
    # 调用API
    result = api.get_following_list(count=5)
    
    if result:
        # 保存完整的API响应
        with open('api_response_following_list.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ API调用成功")
        logger.info(f"响应已保存到: api_response_following_list.json")
        
        # 分析响应结构
        users = result.get("users", [])
        logger.info(f"\n响应分析:")
        logger.info(f"  用户数量: {len(users)}")
        logger.info(f"  有更多: {result.get('hasMore')}")
        logger.info(f"  下一个ID: {result.get('lastId', 'None')}")
        
        for i, user in enumerate(users[:3], 1):
            logger.info(f"\n  用户 {i}:")
            logger.info(f"    名称: {user.get('name')}")
            logger.info(f"    ID: {user.get('id', user.get('userId', ''))}")
    else:
        logger.error("API调用失败，无返回数据")
    
    return result


if __name__ == "__main__":
    logger.info("="*60)
    logger.info("开始测试Finka API响应")
    logger.info("="*60)
    
    # 测试用户作品API
    posts_result = test_user_posts_api()
    
    # 测试关注列表API
    following_result = test_following_list_api()
    
    logger.info("\n" + "="*60)
    logger.info("测试完成！")
    logger.info("生成的文件:")
    logger.info("  - api_response_user_posts.json (用户作品API响应)")
    logger.info("  - api_response_following_list.json (关注列表API响应)")
    logger.info("="*60)