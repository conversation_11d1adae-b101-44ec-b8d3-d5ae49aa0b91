#!/usr/bin/env python3
"""
测试 /feed/user/v3/multi API
对比与普通API的差异
"""

import json
import time
import hashlib
import requests
from urllib.parse import quote

# 认证信息
AUTH_TOKEN = "aq8f3kx3dDvD1wngpZHQ9A|x1KzYzLVeB0|uSNYoRY"
USER_ID = "x1KzYzLVeB0"
SALT = "@B<:OK6"

# 设备信息
DEVICE_HEADERS = {
    "X-App-Auth": AUTH_TOKEN,
    "X-App-Device": "Finka0a%2F8.12.0+%28Android+31%2F12%3B+Google%3B+Crosshatch%3B+Google%3B+Pixel+3+XL%3B+Google+Fi%3B+*********%2FOfficial%3B%29",
    "X-App-GUID": "484837FB-D8BD-4312-A893-8F04B4EE994A",
    "X-App-User": USER_ID,
    "X-App-Version": "8.12.0",
    "X-App-Package": "Finka0a",
    "X-App-Android-Id": "oqkH9k+Tsl0BZ3PnM+/b+3PFLZJYmm6fEwMXkJkNp9c=",
    "X-App-MacAddress": "c8Utkliabp8TAxeQmQ2n1w==",
    "X-App-BlueDeviceInfo": "3wsyM8XQzHOyoIuS1QSUqtKeZjIrHR3gvZpaOScf1mkFc1ylQyFMsqXpStxD/6f9D23pppqJHNjnkn8iT6XGfZ5g",
    "X-App-DigitalAlliance-Id": "9pQ3QzSf7752A5fVFiwyU0GDu2BgVZXfOHr02/X7gSB1cKweXj2mr0HxE9PIYmwC",
    "X-Android-Fingerprint": "google/crosshatch/crosshatch:12/SP1A.210812.015/7679548:user/release-keys",
    "X-Screen-Width": "1440",
    "X-Screen-Height": "2733",
    "X-Screen-TopSafeAreaHeight": "49",
    "X-App-Assid": "",
    "X-App-Imei": "",
    "X-App-Wifi": "",
    "X-App-DeviceName": "",
    "X-App-Oaid": "",
    "User-Agent": "Finka0a/8.12.0 (Android 31/12; Google; Crosshatch; Google; Pixel 3 XL; Google Fi; *********/Official;)",
    "Android-Store": "Official",
    "Accept-Language": "en-US",
    "Accept-Encoding": "gzip",
}

def generate_digest(path, method, headers, query):
    """生成请求签名"""
    # 构建 headers 字符串（只包含 x-app- 开头的，按字母顺序）
    header_parts = []
    for key in sorted(headers.keys()):
        if key.lower().startswith("x-app-"):
            header_parts.append(f"{key.lower()}={headers[key]}")
    header_str = "&".join(header_parts)
    
    # 拼接签名字符串
    sign_str = f"{path}{method}{header_str}{query}{SALT}"
    
    # 计算 MD5
    return hashlib.md5(sign_str.encode()).hexdigest()

def test_multi_api(user_id):
    """测试 /feed/user/v3/multi API"""
    
    print(f"\n测试 /feed/user/v3/multi API (POST)")
    print("=" * 60)
    
    # API 路径
    path = "/feed/user/v3/multi"
    method = "POST"
    
    # 参数
    params = {
        "_t": str(int(time.time() * 1000)),
        "count": "30",
        "uid": user_id
    }
    
    # 构建查询字符串
    query_str = "&".join([f"{k}={v}" for k, v in params.items()])
    
    # 准备 headers
    headers = DEVICE_HEADERS.copy()
    
    # 生成签名
    digest = generate_digest(path, method, headers, query_str)
    headers['Digest'] = f"MD5={digest}"
    headers['Host'] = 'api.finka.cn'
    headers['Connection'] = 'Keep-Alive'
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    
    # 发送请求
    url = f"https://api.finka.cn{path}"
    
    print(f"请求URL: {url}")
    print(f"参数: {params}")
    
    try:
        response = requests.post(url, params=params, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 尝试解析响应
            try:
                data = response.json()
            except:
                # 可能是 gzip 压缩
                import gzip
                content = gzip.decompress(response.content)
                data = json.loads(content)
            
            # 保存响应
            with open('multi_api_response.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"响应已保存到: multi_api_response.json")
            
            # 分析响应
            if data.get('success'):
                items = data.get('data', {}).get('list', [])
                print(f"\n成功获取 {len(items)} 个作品")
                
                # 查找多媒体作品
                multi_media_posts = []
                for item in items:
                    post_id = item.get('postId', '')
                    description = item.get('description', '')
                    
                    # 检查媒体字段
                    media_fields = {}
                    
                    # 检查各种可能的媒体字段
                    if 'imageId' in item:
                        media_fields['imageId'] = item['imageId']
                    if 'videoId' in item:
                        media_fields['videoId'] = item['videoId']
                    if 'images' in item:
                        media_fields['images'] = item['images']
                    if 'videos' in item:
                        media_fields['videos'] = item['videos']
                    if 'imageInfoList' in item:
                        media_fields['imageInfoList'] = item['imageInfoList']
                    if 'mediaList' in item:
                        media_fields['mediaList'] = item['mediaList']
                    if 'media' in item:
                        media_fields['media'] = item['media']
                    
                    # 判断是否是多媒体
                    if '多张图片' in description or '相册' in description or '图组' in description:
                        multi_media_posts.append({
                            'postId': post_id,
                            'description': description,
                            'media_fields': media_fields
                        })
                        
                        print(f"\n发现多媒体作品: {post_id}")
                        print(f"  描述: {description[:50]}...")
                        print(f"  媒体字段:")
                        for field, value in media_fields.items():
                            if isinstance(value, list):
                                print(f"    {field}: 数组，{len(value)}个元素")
                                # 显示前3个
                                for i, v in enumerate(value[:3]):
                                    print(f"      [{i}] {v}")
                            else:
                                print(f"    {field}: {value}")
                
                # 保存多媒体作品分析
                if multi_media_posts:
                    with open('multi_media_analysis.json', 'w', encoding='utf-8') as f:
                        json.dump(multi_media_posts, f, ensure_ascii=False, indent=2)
                    print(f"\n多媒体作品分析已保存到: multi_media_analysis.json")
                    print(f"共找到 {len(multi_media_posts)} 个多媒体作品")
                
                # 返回第一个多媒体作品的完整数据
                if multi_media_posts:
                    first_multi = multi_media_posts[0]
                    for item in items:
                        if item.get('postId') == first_multi['postId']:
                            print(f"\n第一个多媒体作品的完整数据:")
                            print(json.dumps(item, ensure_ascii=False, indent=2)[:2000])
                            break
                
            else:
                print(f"请求失败: {data}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def test_normal_api(user_id):
    """测试普通的 /feed/user/v3 API (GET)"""
    
    print(f"\n测试 /feed/user/v3 API (GET)")
    print("=" * 60)
    
    # API 路径
    path = "/feed/user/v3"
    method = "GET"
    
    # 参数
    params = {
        "_t": str(int(time.time() * 1000)),
        "count": "30",
        "uid": user_id
    }
    
    # 构建查询字符串
    query_str = "&".join([f"{k}={v}" for k, v in params.items()])
    
    # 准备 headers
    headers = DEVICE_HEADERS.copy()
    
    # 生成签名
    digest = generate_digest(path, method, headers, query_str)
    headers['Digest'] = f"MD5={digest}"
    headers['Host'] = 'api.finka.cn'
    headers['Connection'] = 'Keep-Alive'
    
    # 发送请求
    url = f"https://api.finka.cn{path}"
    
    print(f"请求URL: {url}")
    print(f"参数: {params}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 尝试解析响应
            try:
                data = response.json()
            except:
                # 可能是 gzip 压缩
                import gzip
                content = gzip.decompress(response.content)
                data = json.loads(content)
            
            # 保存响应
            with open('normal_api_response.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"响应已保存到: normal_api_response.json")
            
            # 分析响应
            if data.get('success'):
                items = data.get('data', {}).get('list', [])
                print(f"\n成功获取 {len(items)} 个作品")
                
                # 显示前3个作品的媒体字段
                for i, item in enumerate(items[:3]):
                    post_id = item.get('postId', '')
                    description = item.get('description', '')
                    
                    print(f"\n作品 {i+1}: {post_id}")
                    print(f"  描述: {description[:50]}...")
                    
                    # 检查媒体字段
                    for field in ['imageId', 'videoId', 'images', 'videos', 'imageInfoList', 'mediaList']:
                        if field in item:
                            value = item[field]
                            if isinstance(value, list):
                                print(f"  {field}: 数组，{len(value)}个元素")
                                for j, v in enumerate(value[:2]):
                                    if isinstance(v, dict):
                                        print(f"    [{j}] {v.get('imageId', v.get('url', str(v)[:50]))}")
                                    else:
                                        print(f"    [{j}] {v}")
                            else:
                                print(f"  {field}: {value}")
            else:
                print(f"请求失败: {data}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def compare_apis():
    """对比普通API和multi API"""
    
    # 测试的用户（那人/那山@双）
    user_id = "cDBA-mEYce0"
    
    print(f"测试用户: {user_id}")
    
    # 1. 测试普通 API (GET)
    test_normal_api(user_id)
    
    # 2. 测试 multi API (POST)
    test_multi_api(user_id)
    
    print("\n" + "=" * 60)
    print("对比分析")
    print("=" * 60)
    
    # 3. 加载响应进行对比
    try:
        with open('normal_api_response.json', 'r') as f:
            normal_api = json.load(f)
        
        with open('multi_api_response.json', 'r') as f:
            multi_api = json.load(f)
        
        print("\n普通API (/feed/user/v3):")
        if 'data' in normal_api and 'list' in normal_api['data']:
            items = normal_api['data']['list']
            print(f"  返回作品数: {len(items)}")
            if items:
                first = items[0]
                print(f"  第一个作品的媒体字段:")
                for field in ['imageId', 'videoId', 'images', 'videos', 'imageInfoList', 'mediaList']:
                    if field in first:
                        value = first[field]
                        if isinstance(value, list):
                            print(f"    {field}: 数组，{len(value)}个元素")
                        else:
                            print(f"    {field}: {type(value).__name__}")
        
        print("\nMulti API (/feed/user/v3/multi):")
        if 'data' in multi_api and 'list' in multi_api['data']:
            items = multi_api['data']['list']
            print(f"  返回作品数: {len(items)}")
            if items:
                first = items[0]
                print(f"  第一个作品的媒体字段:")
                for field in ['imageId', 'videoId', 'images', 'videos', 'imageInfoList', 'mediaList']:
                    if field in first:
                        value = first[field]
                        if isinstance(value, list):
                            print(f"    {field}: 数组，{len(value)}个元素")
                        else:
                            print(f"    {field}: {type(value).__name__}")
        
        # 找一个具体的多媒体作品对比
        target_post_id = "U15z2BFytuZPrCYDWVbkEQ"  # 春天多张图片
        
        print(f"\n具体作品对比 (ID: {target_post_id}):")
        
        # 在普通API中查找
        normal_post = None
        if 'data' in normal_api and 'list' in normal_api['data']:
            for item in normal_api['data']['list']:
                if item.get('postId') == target_post_id:
                    normal_post = item
                    break
        
        # 在Multi API中查找
        multi_post = None
        if 'data' in multi_api and 'list' in multi_api['data']:
            for item in multi_api['data']['list']:
                if item.get('postId') == target_post_id:
                    multi_post = item
                    break
        
        if normal_post:
            print("  普通API返回:")
            print(f"    描述: {normal_post.get('description', '')[:50]}")
            for field in ['imageId', 'images', 'imageInfoList']:
                if field in normal_post:
                    value = normal_post[field]
                    if isinstance(value, list):
                        print(f"    {field}: {len(value)}个元素")
                    else:
                        print(f"    {field}: {value}")
        
        if multi_post:
            print("  Multi API返回:")
            print(f"    描述: {multi_post.get('description', '')[:50]}")
            for field in ['imageId', 'images', 'imageInfoList']:
                if field in multi_post:
                    value = multi_post[field]
                    if isinstance(value, list):
                        print(f"    {field}: {len(value)}个元素")
                    else:
                        print(f"    {field}: {value}")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
    except Exception as e:
        print(f"对比失败: {e}")

if __name__ == "__main__":
    compare_apis()