#!/usr/bin/env python3
"""
尝试获取作品的详细信息，看是否能获取到多媒体内容
"""

import json
import time
import logging
import requests
from finka import FinkaAPI, Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class ExtendedFinkaAPI(FinkaAPI):
    """扩展的Finka API客户端，尝试不同的API端点"""
    
    def get_post_detail(self, post_id: str):
        """尝试获取作品详情"""
        # 尝试不同的可能端点
        endpoints = [
            f"/post/{post_id}",
            f"/post/detail/{post_id}",
            f"/feed/post/{post_id}",
            f"/feed/detail/{post_id}",
            "/post/detail",
            "/feed/detail"
        ]
        
        for endpoint in endpoints:
            logger.info(f"尝试端点: {endpoint}")
            
            # 如果端点包含ID，使用GET
            if post_id in endpoint:
                result = self.request("GET", endpoint)
            else:
                # 否则尝试POST，带上post_id参数
                result = self.request("POST", endpoint, data={"postId": post_id})
            
            if result and result.get("success"):
                logger.info(f"✅ 成功获取数据从: {endpoint}")
                return result
            elif result:
                logger.warning(f"端点返回但不成功: {result.get('message', 'Unknown error')}")
            
            time.sleep(0.5)
        
        return None
    
    def get_post_media(self, post_id: str):
        """尝试获取作品的媒体列表"""
        endpoints = [
            "/post/media",
            "/feed/media",
            "/post/images",
            "/feed/images"
        ]
        
        for endpoint in endpoints:
            logger.info(f"尝试媒体端点: {endpoint}")
            result = self.request("POST", endpoint, data={"postId": post_id})
            
            if result and result.get("success"):
                logger.info(f"✅ 成功获取媒体数据从: {endpoint}")
                return result
            
            time.sleep(0.5)
        
        return None
    
    def get_user_posts_v2(self, user_id: str, count: int = 10):
        """尝试其他版本的获取用户作品API"""
        endpoints = [
            "/feed/user/v2",
            "/feed/user/v4",
            "/feed/user",
            "/user/posts",
            "/user/feed"
        ]
        
        for endpoint in endpoints:
            logger.info(f"尝试用户作品端点: {endpoint}")
            params = {"count": str(count), "uid": user_id}
            result = self.request("GET", endpoint, params)
            
            if result and result.get("success"):
                logger.info(f"✅ 成功从: {endpoint}")
                return result
            
            time.sleep(0.5)
        
        return None


def test_multi_image_post():
    """测试获取多图片作品的详细信息"""
    api = ExtendedFinkaAPI()
    
    # 使用已知的多图片作品ID
    multi_image_posts = [
        "Zr3Id_lrledPrCYDWVbkEQ",  # 那人/那山@双的作品
        "Av0BhuEaoo9PrCYDWVbkEQ",  # 阿胜l的大理作品
        "eLO6g3CfMPFPrCYDWVbkEQ"   # 阿胜l的3张照片作品
    ]
    
    for post_id in multi_image_posts:
        logger.info(f"\n{'='*60}")
        logger.info(f"测试作品: {post_id}")
        logger.info(f"{'='*60}")
        
        # 1. 尝试获取作品详情
        detail = api.get_post_detail(post_id)
        if detail:
            with open(f'post_detail_{post_id}.json', 'w', encoding='utf-8') as f:
                json.dump(detail, f, ensure_ascii=False, indent=2)
            logger.info(f"详情已保存到: post_detail_{post_id}.json")
            
            # 分析结果
            if 'data' in detail:
                data = detail['data']
                if isinstance(data, dict):
                    logger.info(f"字段检查:")
                    logger.info(f"  images: {'images' in data}")
                    logger.info(f"  image: {'image' in data}")
                    logger.info(f"  media: {'media' in data}")
                    logger.info(f"  mediaList: {'mediaList' in data}")
                    
                    if 'images' in data:
                        logger.info(f"  images数量: {len(data['images'])}")
        
        # 2. 尝试获取媒体列表
        media = api.get_post_media(post_id)
        if media:
            with open(f'post_media_{post_id}.json', 'w', encoding='utf-8') as f:
                json.dump(media, f, ensure_ascii=False, indent=2)
            logger.info(f"媒体数据已保存到: post_media_{post_id}.json")
        
        time.sleep(1)
    
    # 测试其他版本的用户作品API
    logger.info(f"\n{'='*60}")
    logger.info("测试其他版本的用户作品API")
    logger.info(f"{'='*60}")
    
    result = api.get_user_posts_v2("LX5ow083px8", count=5)
    if result:
        with open('user_posts_v2.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.info("其他版本API响应已保存到: user_posts_v2.json")


def analyze_share_link():
    """分析分享链接，看是否能获取更多信息"""
    logger.info(f"\n{'='*60}")
    logger.info("分析分享链接")
    logger.info(f"{'='*60}")
    
    # 分享链接格式：https://www.finkapp.cn/post/finka-{postId}
    post_id = "Av0BhuEaoo9PrCYDWVbkEQ"
    share_url = f"https://www.finkapp.cn/post/finka-{post_id}"
    
    logger.info(f"分享链接: {share_url}")
    
    # 尝试访问网页版
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(share_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # 查找是否有JSON数据或API调用
            content = response.text
            
            # 保存HTML内容
            with open('share_page.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("网页内容已保存到: share_page.html")
            
            # 查找可能的数据
            import re
            
            # 查找JSON数据
            json_pattern = r'<script[^>]*>.*?window\.__INITIAL_STATE__\s*=\s*({.*?})</script>'
            matches = re.findall(json_pattern, content, re.DOTALL)
            
            if matches:
                logger.info("找到嵌入的JSON数据！")
                for i, match in enumerate(matches):
                    try:
                        data = json.loads(match)
                        with open(f'embedded_data_{i}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        logger.info(f"数据已保存到: embedded_data_{i}.json")
                    except:
                        pass
            
            # 查找图片链接
            img_pattern = r'https://pic1\.finkapp\.cn/[^"\']+(?:\.jpg|\.jpeg|\.png)?'
            images = re.findall(img_pattern, content)
            
            if images:
                logger.info(f"找到 {len(set(images))} 个唯一图片链接")
                for img_url in set(images):
                    logger.info(f"  - {img_url}")
                    
    except Exception as e:
        logger.error(f"访问分享链接失败: {e}")


if __name__ == "__main__":
    # 测试获取多图片作品详情
    test_multi_image_post()
    
    # 分析分享链接
    analyze_share_link()
    
    logger.info("\n测试完成！")