#!/usr/bin/env python3
"""
测试Telegram发送媒体文件
"""
import asyncio
from pyrogram import Client
import os

# Telegram 配置
API_ID = 25432929
API_HASH = '965c5d22f0b9d1d0326e84bbb2bb18c1'
BOT_TOKEN = '8056947019:AAHytCVq1NuTZa-WXHWQffchg3gXjgxwRP8'
CHAT_ID = 235196660

async def test_send():
    """测试发送媒体文件"""
    app = Client(
        "test_bot",
        api_id=API_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN
    )
    
    async with app:
        print(f"✅ Bot 已连接")
        
        # 查找一个已下载的媒体文件
        downloads_dir = "/home/<USER>/downloader/downloads"
        
        # 查找第一个视频文件
        video_file = None
        image_file = None
        
        for root, dirs, files in os.walk(downloads_dir):
            for file in files:
                if file.endswith('.mp4') and not video_file:
                    video_file = os.path.join(root, file)
                if file.endswith('.jpg') and not image_file:
                    image_file = os.path.join(root, file)
                if video_file and image_file:
                    break
            if video_file and image_file:
                break
        
        # 发送测试消息
        try:
            # 1. 发送文本消息
            message = await app.send_message(
                chat_id=CHAT_ID,
                text="🎉 测试消息：Finka Bot 已连接成功!"
            )
            print(f"✅ 文本消息发送成功: {message.id}")
            
            # 2. 发送图片
            if image_file and os.path.exists(image_file):
                print(f"📸 发送图片: {image_file}")
                photo_msg = await app.send_photo(
                    chat_id=CHAT_ID,
                    photo=image_file,
                    caption="🖼️ 测试图片发送"
                )
                print(f"✅ 图片发送成功: {photo_msg.photo.file_id}")
            
            # 3. 发送视频
            if video_file and os.path.exists(video_file):
                print(f"🎬 发送视频: {video_file}")
                video_msg = await app.send_video(
                    chat_id=CHAT_ID,
                    video=video_file,
                    caption="🎥 测试视频发送",
                    thumb=image_file if image_file else None
                )
                print(f"✅ 视频发送成功: {video_msg.video.file_id}")
            
            print("\n🎉 所有测试完成!")
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_send())