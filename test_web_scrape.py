#!/usr/bin/env python3
"""
尝试从网页版获取多媒体内容
"""

import json
import re
import requests
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def scrape_share_page(post_id):
    """抓取分享页面内容"""
    share_url = f"https://www.finkapp.cn/post/finka-{post_id}"
    
    logger.info(f"访问分享页面: {share_url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        response = requests.get(share_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 保存HTML
            with open(f'share_{post_id}.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"HTML已保存到: share_{post_id}.html")
            
            # 查找所有图片链接
            img_patterns = [
                r'https://pic1\.finkapp\.cn/[a-zA-Z0-9_-]+',
                r'"imageId":"([a-zA-Z0-9_-]+)"',
                r'imageId=([a-zA-Z0-9_-]+)',
                r'data-src="(https://pic1\.finkapp\.cn/[^"]+)"',
                r'src="(https://pic1\.finkapp\.cn/[^"]+)"'
            ]
            
            all_images = set()
            for pattern in img_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match.startswith('http'):
                        # 清理URL参数
                        clean_url = match.split('?')[0]
                        all_images.add(clean_url)
                    else:
                        # 这是imageId
                        all_images.add(f"https://pic1.finkapp.cn/{match}")
            
            logger.info(f"找到 {len(all_images)} 个唯一图片URL")
            for img_url in all_images:
                logger.info(f"  - {img_url}")
            
            # 查找JSON数据
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({[^<]+})',
                r'window\.__DATA__\s*=\s*({[^<]+})',
                r'<script type="application/json"[^>]*>([^<]+)</script>',
                r'data-json="([^"]+)"'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                for i, match in enumerate(matches):
                    try:
                        # 处理转义字符
                        if pattern == r'data-json="([^"]+)"':
                            match = match.replace('&quot;', '"').replace('&#x27;', "'")
                        
                        data = json.loads(match)
                        with open(f'json_data_{post_id}_{i}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        logger.info(f"JSON数据已保存到: json_data_{post_id}_{i}.json")
                        
                        # 分析数据结构
                        analyze_json_for_media(data)
                        
                    except Exception as e:
                        logger.warning(f"解析JSON失败: {e}")
            
            return all_images
            
        else:
            logger.error(f"访问失败: HTTP {response.status_code}")
            
    except Exception as e:
        logger.error(f"抓取页面失败: {e}")
    
    return None

def analyze_json_for_media(data, prefix=""):
    """递归分析JSON数据查找媒体内容"""
    if isinstance(data, dict):
        for key, value in data.items():
            if key in ['images', 'mediaList', 'media', 'imageList', 'photos']:
                logger.info(f"找到媒体字段 {prefix}{key}: {type(value)}")
                if isinstance(value, list):
                    logger.info(f"  包含 {len(value)} 个项目")
                    for item in value[:3]:  # 显示前3个
                        logger.info(f"    - {item}")
            elif key in ['imageId', 'videoId', 'url'] and isinstance(value, str):
                if 'fink' in value or len(value) > 10:
                    logger.info(f"找到媒体ID/URL {prefix}{key}: {value}")
            
            if isinstance(value, (dict, list)):
                analyze_json_for_media(value, f"{prefix}{key}.")

def test_multiple_posts():
    """测试多个已知的多图片作品"""
    multi_image_posts = [
        ("Zr3Id_lrledPrCYDWVbkEQ", "那人/那山@双 - 多张图片"),
        ("U15z2BFytuZPrCYDWVbkEQ", "那人/那山@双 - 春天多张图片"),
        ("Av0BhuEaoo9PrCYDWVbkEQ", "阿胜l - 大理多张图片"),
        ("eLO6g3CfMPFPrCYDWVbkEQ", "阿胜l - 3张好看的照片")
    ]
    
    all_results = {}
    
    for post_id, description in multi_image_posts:
        logger.info(f"\n{'='*60}")
        logger.info(f"测试: {description}")
        logger.info(f"作品ID: {post_id}")
        logger.info(f"{'='*60}")
        
        images = scrape_share_page(post_id)
        
        if images:
            all_results[post_id] = {
                'description': description,
                'images': list(images)
            }
    
    # 保存所有结果
    with open('multi_image_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"\n所有结果已保存到: multi_image_analysis.json")
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("总结")
    logger.info(f"{'='*60}")
    for post_id, info in all_results.items():
        logger.info(f"{info['description']}:")
        logger.info(f"  找到 {len(info['images'])} 个图片")

if __name__ == "__main__":
    test_multiple_posts()