const { createClient } = require('@supabase/supabase-js');
const Redis = require('ioredis');

// 配置部分
const url = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';

const supabase = createClient(url, key);
const redis = new Redis();

const PROGRAM_TAG = 'jp2_tag'; // 假设本程序的PROGRAM_TAG是 home_tag

function getCurrentTime() {
  return new Date().toLocaleString();
}

let doesValue = null;
let doesData = null; // 保存从does表中读出的整行数据，用于确定其他_tag字段内容

/**
 * 统计信息输出：
 *  - 本次共打了多少个tag
 *  - 现在数据库中属于我们的tag共有多少行
 */
async function showTagStats(updatedCount) {
  console.log(`[${getCurrentTime()}] 本次共打了${updatedCount}个tag`);

  const { data, error, count } = await supabase
    .from('anchors')
    .select('*', { count: 'exact' })
    .eq('tag', PROGRAM_TAG);

  if (error) {
    console.error(`[${getCurrentTime()}] 获取tag数量时出错: ${error.message}`);
    return;
  }

  console.log(`[${getCurrentTime()}] 现在数据库中属于我们的tag共有${count}行`);
}

async function updateDoesValue() {
  try {
    const { data, error } = await supabase
      .from('does')
      .select('*')
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 从 does 表读取值时出错: ${error.message}`);
      return;
    }

    doesValue = data[PROGRAM_TAG];
    doesData = data;
    console.log(`[${getCurrentTime()}] 当前 does 值(${PROGRAM_TAG}): ${doesValue}`);
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新 does 值时发生异常: ${err.message}`);
  }
}

// 判断是否是range模式值
function isRangeMode(value) {
  return typeof value === 'string' && /^\[\d+,\d+\)$/.test(value.trim());
}

// 解析range字符串，如"[0,500)" -> {start:0, end:500}
function parseRange(value) {
  const match = value.trim().match(/^\[(\d+),(\d+)\)$/);
  if (!match) return null;
  return { start: parseInt(match[1], 10), end: parseInt(match[2], 10) };
}

async function tagAnchors() {
  try {
    if (!doesValue) {
      console.log(`[${getCurrentTime()}] doesValue为空，不执行打标签操作`);
      return;
    }

    const rangeMode = isRangeMode(doesValue);

    if (rangeMode) {
      // ---------------------------
      // range模式
      // ---------------------------
      const range = parseRange(doesValue);
      if (!range) {
        console.log(`[${getCurrentTime()}] doesValue格式不正确，无法解析为range: ${doesValue}`);
        return;
      }

      // 从doesData中获取所有非range且非空的_tag字段值，这些是platform模式值，需要过滤掉对应的平台
      const otherPlatforms = [];
      for (const key in doesData) {
        if (key.endsWith('_tag') && key !== PROGRAM_TAG) {
          const val = doesData[key];
          // val为空或null则不考虑
          if (!val) {
            continue;
          }
          // 非range且非空为platform值，需要过滤
          if (!isRangeMode(val)) {
            otherPlatforms.push(val);
          }
        }
      }

      console.log(`[${getCurrentTime()}] range模式中要忽略的platform列表: ${otherPlatforms.join(', ')}`);

      // 查询符合条件的anchors:
      // 1. nosub=false
      // 2. platform不在otherPlatforms中（用neq循环排除）
      // 3. 按timestamp升序排序
      let query = supabase
        .from('anchors')
        .select('anchor_id, platform, new_url, tag, nosub, timestamp')
        .eq('nosub', false);

      // 对每个需过滤的平台使用 neq 进行排除
      otherPlatforms.forEach(p => {
        query = query.neq('platform', p);
      });

      query = query.order('timestamp', { ascending: true });

      const { data: allAnchors, error: selectError } = await query;
      if (selectError) {
        console.error(`[${getCurrentTime()}] 查询range模式条目时出错: ${selectError.message}`);
        return;
      }

      // 取 [start,end) 范围内的条目
      const sliceAnchors = allAnchors.slice(range.start, range.end);

      if (sliceAnchors.length === 0) {
        console.log(`[${getCurrentTime()}] range模式中在指定范围[${range.start},${range.end})无条目需要打标`);
        return;
      }

      console.log(`[${getCurrentTime()}] 即将在range模式下为以下条目打上tag=${PROGRAM_TAG}:`);
      sliceAnchors.forEach(anchor => {
        console.log(`[${getCurrentTime()}] anchor_id=${anchor.anchor_id}, platform=${anchor.platform}, new_url=${anchor.new_url}, nosub=${anchor.nosub}, 原tag=${anchor.tag}`);
      });

      const anchorIdsToUpdate = sliceAnchors.map(a => a.anchor_id);

      const { data: updatedAnchors, error: updateError } = await supabase
        .from('anchors')
        .update({ tag: PROGRAM_TAG })
        .in('anchor_id', anchorIdsToUpdate)
        .select('anchor_id, platform, new_url, tag, nosub, timestamp');

      if (updateError) {
        console.error(`[${getCurrentTime()}] range模式打tag时出错: ${updateError.message}`);
        return;
      }

      console.log(`[${getCurrentTime()}] range模式打tag完成，更新后条目:`);
      updatedAnchors.forEach(anchor => {
        console.log(`[${getCurrentTime()}] anchor_id=${anchor.anchor_id}, platform=${anchor.platform}, new_url=${anchor.new_url}, nosub=${anchor.nosub}, 新tag=${anchor.tag}`);
      });

      // 打完tag后输出统计信息
      await showTagStats(updatedAnchors.length);

    } else {
      // ---------------------------
      // platform模式 (支持多平台)
      // ---------------------------
      // 如果 doesValue 中包含逗号，视为多个平台
      const platforms = doesValue
        .split(',')
        .map(p => p.trim())
        .filter(p => p.length > 0);

      if (platforms.length === 0) {
        console.log(`[${getCurrentTime()}] doesValue(${doesValue})未解析出任何平台`);
        return;
      }

      // 从 anchors 表中查询需要更新的记录
      // 1. platform 在 platforms 数组中
      // 2. nosub = false
      // 3. tag != PROGRAM_TAG
      const { data: anchorsToUpdate, error: selectError } = await supabase
        .from('anchors')
        .select('anchor_id, platform, new_url, tag, nosub')
        .in('platform', platforms)
        .eq('nosub', false)
        .neq('tag', PROGRAM_TAG);

      if (selectError) {
        console.error(`[${getCurrentTime()}] 查询platform模式条目时出错: ${selectError.message}`);
        return;
      }

      if (anchorsToUpdate.length === 0) {
        console.log(`[${getCurrentTime()}] platform模式下无需要更新tag的条目`);
        return;
      }

      console.log(`[${getCurrentTime()}] 即将在platform模式下为以下条目打上tag=${PROGRAM_TAG}:`);
      anchorsToUpdate.forEach(anchor => {
        console.log(`[${getCurrentTime()}] anchor_id=${anchor.anchor_id}, platform=${anchor.platform}, new_url=${anchor.new_url}, nosub=${anchor.nosub}, 原tag=${anchor.tag}`);
      });

      const anchorIdsToUpdate = anchorsToUpdate.map(a => a.anchor_id);

      // 更新 tag
      const { data: updatedAnchors, error: updateError } = await supabase
        .from('anchors')
        .update({ tag: PROGRAM_TAG })
        .in('anchor_id', anchorIdsToUpdate)
        .select('anchor_id, platform, new_url, tag, nosub');

      if (updateError) {
        console.error(`[${getCurrentTime()}] platform模式打tag时出错: ${updateError.message}`);
        return;
      }

      console.log(`[${getCurrentTime()}] platform模式打tag完成，更新后条目:`);
      updatedAnchors.forEach(anchor => {
        console.log(`[${getCurrentTime()}] anchor_id=${anchor.anchor_id}, platform=${anchor.platform}, new_url=${anchor.new_url}, nosub=${anchor.nosub}, 新tag=${anchor.tag}`);
      });

      // 打完tag后输出统计信息
      await showTagStats(updatedAnchors.length);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] tagAnchors异常: ${err.message}`);
  }
}

async function doScanall() {
  try {
    const { data: anchors, error } = await supabase
      .from('anchors')
      .select('anchor_id, new_url, platform')
      .eq('tag', PROGRAM_TAG);

    if (error) {
      console.error(`[${getCurrentTime()}] 获取tag=${PROGRAM_TAG}的anchors时出错: ${error.message}`);
      return;
    }

    let dataCounter = 0;
    console.log(`[${getCurrentTime()}] 开始scanall传输，共${anchors.length}条数据（tag=${PROGRAM_TAG}）`);
    for (const anchor of anchors) {
      const message = `${anchor.anchor_id} ${anchor.new_url} ${anchor.platform}`;
      console.log(`[${getCurrentTime()}] scanall发送: ${message}`);
      redis.publish('new', message);
      dataCounter++;
    }

    console.log(`[${getCurrentTime()}] scanall传输完成，共传输 ${dataCounter} 条数据（tag=${PROGRAM_TAG}）`);
  } catch (err) {
    console.error(`[${getCurrentTime()}] scanall时发生异常: ${err.message}`);
  }
}

// 检查does表中是否有任意_tag字段变动的辅助函数
function hasAnyTagFieldChanged(oldRecord, newRecord) {
  for (const key in newRecord) {
    if (key.endsWith('_tag')) {
      if (oldRecord[key] !== newRecord[key]) {
        console.log(`[${getCurrentTime()}] _tag字段变化检测: ${key} 旧值=${oldRecord[key]}, 新值=${newRecord[key]}`);
        return true;
      }
    }
  }
  return false;
}

// does表变化事件
const onDoesChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  let needRetag = false;
  let needScanall = false;

  // 任意_tag字段变化 => 需要重新打标签
  if (hasAnyTagFieldChanged(oldRecord, newRecord)) {
    console.log(`[${getCurrentTime()}] does表中存在_tag字段变化，需要重新打标签`);
    needRetag = true;
  }

  // scanall字段变化 => 调用doScanall()
  if (oldRecord.scanall !== newRecord.scanall) {
    console.log(`[${getCurrentTime()}] does表的scanall字段发生变化，旧值=${oldRecord.scanall}, 新值=${newRecord.scanall}，需要进行scanall操作`);
    needScanall = true;
  }

  if (needRetag) {
    await updateDoesValue();
    await tagAnchors();
  }

  if (needScanall) {
    await doScanall();
  }
};

// anchors更新事件，用于nosub变化时触发打tag
const onAnchorsUpdate = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  // nosub变化 => 重新打标签
  if (oldRecord.nosub !== newRecord.nosub) {
    console.log(`[${getCurrentTime()}] anchors表 nosub变化，anchor_id=${newRecord.anchor_id}, 旧nosub=${oldRecord.nosub}, 新nosub=${newRecord.nosub}，重新打标签`);
    await updateDoesValue();
    await tagAnchors();
  }
};

// 新插入记录事件
const onInsert = async (payload) => {
  const newRecord = payload.new;

  // 如果tag=PROGRAM_TAG且nosub=false则发送到new频道
  if (newRecord.tag === PROGRAM_TAG && newRecord.nosub === false) {
    console.log(`[${getCurrentTime()}] 插入新记录，符合条件：anchor_id=${newRecord.anchor_id}, tag=${newRecord.tag}, nosub=${newRecord.nosub}, platform=${newRecord.platform}, new_url=${newRecord.new_url}`);
    const anchorId = newRecord.anchor_id;
    const newUrl = newRecord.new_url;
    const platform = newRecord.platform;
    const message = `${anchorId} ${newUrl} ${platform}`;
    console.log(`[${getCurrentTime()}] 发送消息到 Redis 'new' 频道: ${message}`);
    redis.publish('new', message);
  } else {
    console.log(`[${getCurrentTime()}] 插入新记录但不符合处理条件：anchor_id=${newRecord.anchor_id}, tag=${newRecord.tag}, nosub=${newRecord.nosub}`);
  }
};

(async () => {
  await updateDoesValue();
  await tagAnchors();
  console.log(`[${getCurrentTime()}] 初始化完成，开始监听数据库变更`);
})();

supabase
  .channel('anchors-changes')
  .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'anchors' }, onAnchorsUpdate)
  .subscribe();

supabase
  .channel('anchors-insert')
  .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'anchors' }, onInsert)
  .subscribe();

supabase
  .channel('does-changes')
  .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'does' }, onDoesChange)
  .subscribe();

process.on('SIGINT', async () => {
  console.log(`\n[${getCurrentTime()}] 接收到退出信号...`);
  await redis.quit();
  await supabase.removeAllChannels();
  console.log(`[${getCurrentTime()}] 程序已安全退出`);
  process.exit(0);
});
